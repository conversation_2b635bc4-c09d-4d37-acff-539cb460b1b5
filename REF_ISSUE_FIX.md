# 修复 ref?.current 为 undefined 的问题

## 问题描述
在按需加载组件的机制下，当调用 `getSubmit` 方法时，`ref?.current` 为 `undefined`，导致无法调用子组件的方法。

## 问题原因
1. **组件未加载**: 在按需加载机制下，如果组件还没有被渲染过，对应的组件就不会被加载，ref 也不会被创建。
2. **异步加载**: React.lazy 组件需要时间来加载和挂载，即使调用了 `loadComponent`，组件的 ref 可能还没有准备好。
3. **时序问题**: 在组件完全挂载之前就尝试调用其方法。

## 解决方案

### 1. 确保组件已加载
在调用组件方法之前，先调用 `loadComponent` 确保组件已经被加载：

```javascript
const getSubmit = () => {
  // 确保组件已加载
  const Component = loadComponent(currentRef.current);
  if (!Component) {
    console.warn(`组件未找到，步骤: ${currentRef.current}`);
    return;
  }
  
  // 后续逻辑...
};
```

### 2. 添加 ref 状态检查
在调用组件方法之前，检查 ref 是否已经准备好：

```javascript
const ref = refs.current[currentRef.current];
if (!ref?.current) {
  console.warn(`组件 ref 未准备好，步骤: ${currentRef.current}`);
  return;
}
```

### 3. 添加延迟等待
由于 React.lazy 组件的异步特性，添加一个短暂的延迟确保组件完全挂载：

```javascript
// 对于同步方法
setTimeout(() => {
  const ref = refs.current[currentRef.current];
  if (ref?.current) {
    ref.current.someMethod?.();
  }
}, 100);

// 对于异步方法
await new Promise(resolve => setTimeout(resolve, 100));
const ref = refs.current[currentRef.current];
if (ref?.current) {
  await ref.current.someMethod?.();
}
```

## 修改的方法

### 1. getSubmit 方法
- 添加组件加载检查
- 使用 setTimeout 确保组件挂载完成
- 添加 ref 状态验证

### 2. getSubmitInfo 方法
- 添加组件加载检查
- 使用 Promise 延迟等待组件挂载
- 添加 ref 状态验证

### 3. handleIncrement 方法
- 添加组件加载检查
- 使用 Promise 延迟等待组件挂载
- 添加 ref 状态验证

## 技术细节

### loadComponent 函数优化
修改了 `loadComponent` 函数，使其能够立即缓存组件，避免异步状态更新的问题：

```javascript
// 立即缓存已加载的组件，避免异步问题
const newLoadedComponents = {
  ...loadedComponents,
  [stepIndex]: ComponentWrapper,
};
setLoadedComponents(newLoadedComponents);
```

### 错误处理
添加了详细的错误日志，帮助调试：
- 组件未找到的警告
- ref 未准备好的警告
- 步骤信息的日志输出

## 测试建议

1. **测试组件加载**: 确保在调用组件方法时组件已经被正确加载
2. **测试 ref 状态**: 验证 ref.current 不为 undefined
3. **测试方法调用**: 确保子组件的方法能够被正确调用
4. **测试时序**: 验证在不同时机调用方法都能正常工作

## 注意事项

1. **延迟时间**: 100ms 的延迟是一个经验值，可能需要根据实际情况调整
2. **错误处理**: 如果组件加载失败，方法会提前返回，不会执行后续逻辑
3. **性能影响**: 添加的延迟对用户体验影响很小，但确保了功能的可靠性
4. **兼容性**: 修改保持了原有的 API 接口，不影响现有功能
