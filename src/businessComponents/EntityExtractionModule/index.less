.entity-extraction{
  .split-preview-card{
    margin-bottom: var(--ant-margin-sm);
    .ant-card-body{
      padding: var(--ant-margin);
    }
  }
  .entity-file-list{
    background: var(--ant-color-bg-container);
    padding: 0px 40px 0px 50px;
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      margin-bottom: 18px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .entity-file-view{
    background: var(--ant-color-bg-container);
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .entity-right{
    padding:0px 30px;
    background: var(--ant-color-bg-container);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.02),0px 1px 6px -1px rgba(0, 0, 0, 0.02),0px 1px 2px 0px rgba(0, 0, 0, 0.03);
    .contract-type{
      height: 65px;
      color:"#333";
      position: absolute;
      right: 30px;
      top: 0px;
      span{
        font-weight:bold;
      }
    }
  }
}
.rule-modal-style{
  .ant-tabs-nav{
    &::before {
      border-bottom:0px;
    }
  }
  .ant-modal-content{
    padding: 10px 30px 30px;
    .ant-form-item-label{
      font-size: var(--ant-font-size-lg);
      font-weight: 500;
      color: #333;
      padding:0px;
    }
    .title{
      font-size: var(--ant-font-size-lg);
      font-weight: 500;
      line-height: var(--ant-line-height-lg);
      letter-spacing: 0.59px;
      color: #333333;
    }
  }
}