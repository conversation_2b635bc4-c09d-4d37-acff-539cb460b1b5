import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import { Card, Tag, Button, Divider, message } from 'antd';
import './index.less';

export interface QuestionModuleRef {
  getQuestionData: () => any;
  setQuestionData: (data: any) => void;
  generateQuestions: () => Promise<void>;
}

interface QuestionModuleProps {
}

interface QuestionItem {
  id: string;
  title: string;
  category: string;
  examinePoints: string[];
  answerPoints: string[];
  scoringCriteria: Array<{
    criterion: string;
    weight: number;
  }>;
}

const QuestionModule = forwardRef<QuestionModuleRef, QuestionModuleProps>(({ 
}, ref) => {
  const [questions, setQuestions] = useState<QuestionItem[]>([
    {
      id: '1',
      title: '作为产品经理,你如何平衡市场推广活动的创意设计需求与技术实现可行性?',
      category: '技术',
      examinePoints: ['技术理解能力', '需求分析能力'],
      answerPoints: ['需求优先级排序', '技术可行性评估'],
      scoringCriteria: [
        { criterion: '逻辑清晰性', weight: 30 },
        { criterion: '技术理解', weight: 40 },
        { criterion: '实际案例', weight: 30 }
      ]
    },
    {
      id: '2',
      title: '作为产品经理,你如何平衡市场推广活动的创意设计需求与技术实现可行性?',
      category: '技术',
      examinePoints: ['技术理解能力', '需求分析能力'],
      answerPoints: ['需求优先级排序', '技术可行性评估'],
      scoringCriteria: [
        { criterion: '逻辑清晰性', weight: 30 },
        { criterion: '技术理解', weight: 40 },
        { criterion: '实际案例', weight: 30 }
      ]
    },
    {
      id: '3',
      title: '请分享你使用Photoshop和Illustrator设计推广物料的具体工作流程',
      category: '设计',
      examinePoints: ['设计工具使用能力', '工作流程规范性'],
      answerPoints: ['需求分析', '设计规划', '制作执行', '反馈优化'],
      scoringCriteria: [
        { criterion: '工具熟练度', weight: 40 },
        { criterion: '流程规范性', weight: 35 },
        { criterion: '设计质量', weight: 25 }
      ]
    }
  ]);
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getQuestionData: () => ({
      questions,
      totalCount: questions.length,
      categories: [...new Set(questions.map(q => q.category))]
    }),
    setQuestionData: (data: any) => {
      if (data.questions) {
        setQuestions(data.questions);
      }
    },
    generateQuestions: async () => {
      await handleGenerateQuestions();
    },
  }), [questions]);

  // 生成题目
  const handleGenerateQuestions = async () => {
    setIsGenerating(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newQuestions = [
        {
          id: Date.now().toString(),
          title: '请描述你在产品迭代过程中如何处理用户反馈和需求变更？',
          category: '产品管理',
          examinePoints: ['需求管理能力', '用户思维'],
          answerPoints: ['用户反馈收集', '需求优先级评估', '变更管理流程'],
          scoringCriteria: [
            { criterion: '需求理解', weight: 35 },
            { criterion: '流程管理', weight: 35 },
            { criterion: '沟通协调', weight: 30 }
          ]
        }
      ];
      
      setQuestions(prev => [...prev, ...newQuestions]);
      
      message.success('题目生成成功！');
    } catch (error) {
      message.error('题目生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };


  // 过滤题目
  const filteredQuestions = selectedCategory === 'all' 
    ? questions 
    : questions.filter(q => q.category === selectedCategory);

  const categories = [...Array.from(new Set(questions.map(q => q.category)))];

  return (
    <div className="question-module">
      <div className="question-header">
        <h3>智能题目生成</h3>
        <div className="question-actions">
          {/* <Button 
            type="primary" 
            onClick={handleGenerateQuestions}
            loading={isGenerating}
            style={{ marginRight: 8 }}
          >
            生成新题目
          </Button>
        </div>
      </div>

      <div className="question-filters">
        <div className="category-filter">
          {/* <span>题目分类：</span> */}
          {categories.map(category => (
            <Tag
              key={category}
              color={selectedCategory === category ? 'blue' : 'default'}
              style={{ cursor: 'pointer', marginRight: 8 }}
              onClick={() => setSelectedCategory(category)}
            >
              {category === 'all' ? '全部' : category}
            </Tag>
          ))}
        </div>
        {/* <div className="question-count">
          共 {filteredQuestions.length} 道题目
        </div> */}
      </div>

      <div className="questions-list">
        {filteredQuestions.map((question, index) => (
          <Card 
            key={question.id} 
            className="question-item"
            style={{ marginBottom: 16 }}
          >
            <div className="question-header">
              <h4>{index + 1}. {question.title}</h4>
              <Tag color="blue">{question.category}</Tag>
            </div>
            
            <div className="question-details">
              <div className="detail-section">
                <strong>考察要点：</strong>
                {/* <div className="tags-list">
                  {question.examinePoints.map((point, idx) => (
                    <Tag key={idx} color="green">{point}</Tag>
                  ))}
                </div> */}
              </div>
              
              <div className="detail-section">
                <strong>参考答案要点：</strong>
                {/* <div className="tags-list">
                  {question.answerPoints.map((point, idx) => (
                    <Tag key={idx} color="orange">{point}</Tag>
                  ))}
                </div> */}
              </div>
              
              <div className="detail-section">
                <strong>评分标准：</strong>
                {/* <div className="scoring-criteria">
                  {question.scoringCriteria.map((criterion, idx) => (
                    <div key={idx} className="criterion-item">
                      <span>{criterion.criterion}</span>
                      <span className="weight">({criterion.weight}%)</span>
                    </div>
                  ))}
                </div> */}
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredQuestions.length === 0 && (
        <div className="no-questions">
          <p>暂无题目，点击"生成新题目"开始生成</p>
        </div>
      )}
    </div>
  );
});

QuestionModule.displayName = 'QuestionModule';

export default QuestionModule;
