.evaluation-report-module {
  width: 100%;
  height: 100%;
  // background: #f5f5f5;
  padding: 50px 20px 20px;
  
  .interview-container {
    gap: 30px;
    display: flex;
    height: 100%;
    // min-height: 600px;
    
    .left-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .left-tabs {
        height: 100%;
        
        .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow-y: auto;
        }
        
        .ant-tabs-tabpane {
          height: 100%;
          // padding: 20px;
        }
      }
    }
    
    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .right-tabs {
        flex: 1; // Changed from height: 100%
      }

      // Tab内容区域样式
      .analysis-content,
      .matching-content,
      .question-content,
      .meeting-content,
      .report-content {
        // padding: 10px;
        height: 100%;
        
        .component-wrapper {
          height: 100%;
        }
      }
      
      // 分析内容样式
      .analysis-content {
        .component-wrapper {
          // border-left: 4px solid #52c41a;
        }
        
        // 加载状态样式
                    .loading-state {
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 300px;
              text-align: center;
              
              .loading-text {
                h3 {
                  color: #1890ff;
                  margin-bottom: 16px;
                  font-size: 20px;
                }
                
                p {
                  color: #666;
                  margin-bottom: 24px;
                  font-size: 14px;
                }
              }
            }
        
        // 分析结果样式
        .analysis-result {
          padding: 20px;
          
          h3 {
            color: #52c41a;
            margin-bottom: 16px;
            font-size: 18px;
          }
          
          .result-content {
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 16px;
            
            pre {
              margin: 0;
              white-space: pre-wrap;
              word-wrap: break-word;
              font-size: 12px;
              color: #333;
            }
          }
        }
      }
      

      // 报告内容样式
      .report-content {
        .interview-report {
          max-width: 800px;
          margin: 0 auto;
          padding: 0 20px;
          height: 100%;
        }
        .report-card {
          border: none;
          height: 100%;
          .ant-card-head {
            border-bottom: none;
            padding: 0;
          }
          .ant-card-body {
            height: calc(100% - 56px);
            overflow-y: auto;
            padding: 0;
          }
        }
        .report-text {
          line-height: 1.8;
          font-size: 14px;
          color: #262626;
          white-space: pre-wrap;
          word-break: break-word;
        }
        .markdown-content {
          line-height: 1.8;
          font-size: 14px;
          color: #262626;
    
          h1, h2, h3, h4, h5, h6 {
            color: #1890ff;
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
          }
    
          h1 {
            font-size: 24px;
            // border-bottom: 2px solid #f0f0f0;
            // padding-bottom: 8px;
          }
    
          h2 {
            font-size: 20px;
          }
    
          h3 {
            font-size: 18px;
          }
    
          p {
            margin-bottom: 16px;
            line-height: 1.8;
          }
    
          ul, ol {
            margin-bottom: 16px;
            padding-left: 24px;
    
            li {
              margin-bottom: 8px;
              line-height: 1.6;
            }
          }
    
          blockquote {
            border-left: 4px solid #1890ff;
            padding-left: 16px;
            margin: 16px 0;
            background-color: #f6ffed;
            padding: 16px;
            border-radius: 6px;
          }
    
          code {
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
          }
    
          pre {
            background-color: #f5f5f5;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 16px 0;
    
            code {
              background: none;
              padding: 0;
            }
          }
    
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
    
            th, td {
              border: 1px solid #d9d9d9;
              padding: 8px 12px;
              text-align: left;
            }
    
            th {
              background-color: #fafafa;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  
  .bottom-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 简历预览样式
.resume-preview {
  .resume-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .resume-info {
      flex: 1;
      
      h2 {
        margin: 0 0 8px 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }
      
      .basic-info {
        margin: 8px 0;
        color: #666;
        font-size: 14px;
      }
      
      .contact-info {
        margin: 8px 0;
        color: #999;
        font-size: 13px;
      }
    }
    
    .resume-avatar {
      margin-left: 20px;
    }
  }
  
  .resume-section {
    margin-bottom: 24px;
    
    h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
      border-left: 4px solid #1890ff;
      padding-left: 12px;
    }
    
    .advantage-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .ant-tag {
        margin: 0;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
      }
    }
    
    .work-item {
      margin-bottom: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      border-left: 3px solid #1890ff;
      
      .work-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .company {
          font-weight: 600;
          color: #1890ff;
          font-size: 14px;
        }
        
        .position {
          color: #333;
          font-size: 14px;
        }
        
        .duration {
          color: #999;
          font-size: 12px;
        }
      }
      
      .work-description {
        margin: 0;
        color: #666;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}

// 简历解析样式
.resume-analysis {
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),0px 3px 6px -4px rgba(0, 0, 0, 0.12),0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  .candidate-info {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 12px 0;
      color: #1890ff;
      font-size: 20px;
      font-weight: 600;
    }
    
    .candidate-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;
      
      .ant-tag {
        margin: 0;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        
        &.ant-tag-red {
          background: #fff2f0;
          border-color: #ffccc7;
          color: #cf1322;
        }
        
        &.ant-tag-green {
          background: #f6ffed;
          border-color: #b7eb8f;
          color: #389e0d;
        }
      }
    }
    
    .candidate-details {
      margin: 0;
      color: #666;
      font-size: 13px;
      line-height: 1.5;
    }
  }
  
  .analysis-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
        border-radius: 2px;
      }
    }
    
    .highlights-list,
    .risks-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #666;
        font-size: 13px;
        line-height: 1.5;
        
        &.risk-item {
          color: #cf1322;
        }
      }
    }
  }
}

// 模板内容样式
.template-content {
  padding: 40px;
  text-align: center;
  color: #999;
  font-size: 16px;
}

// 岗位匹配样式
.job-matching {
  padding: 40px;
  text-align: center;
  color: #999;
  font-size: 16px;
}

// 底部按钮区域样式
.right-buttons {
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #e8e8e8;
  
  .button-group {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    
    .ant-btn {
      min-width: 100px;
      height: 36px;
      border-radius: 6px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;
        
        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
      
      &:not(.ant-btn-primary) {
        border-color: #d9d9d9;
        color: #666;
        
        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }
  }
}

// // 响应式设计
// @media (max-width: 1200px) {
//   .interview-module {
//     .interview-container {
//       flex-direction: column;
//       height: auto;
//       
//       .left-section,
//       .right-section {
//         flex: none;
//         height: auto;
//         min-height: 400px;
//       }
//     }
//   }
// }

// @media (max-width: 768px) {
//   .interview-module {
//     padding: 10px;
//     
//     .interview-container {
//       gap: 10px;
//       
//       .left-section,
//       .right-section {
//         .left-tabs,
//         .right-tabs {
//           .ant-tabs-tabpane {
//             padding: 15px;
//           }
//         }
//       }
//     }
//     
//     .bottom-actions {
//       flex-direction: column;
//       align-items: center;
//       
//       .ant-btn {
//         width: 100%;
//         max-width: 200px;
//       }
//     }
//   }
// }

// 加载状态样式已使用Ant Design的Spin组件
