.app-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  position: relative;

  .header {
    display: flex;
    justify-content: center;
    padding: 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 10;

    .upload-btn {
      margin: 0 15px;
    }
  }

  .btn {
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s;
    border: none;
    font-size: 14px;

    &.left-btn {
      background-color: #ff6b6b;
      color: white;

      &:hover {
        background-color: #ff5252;
        transform: translateY(-2px);
      }
    }

    &.right-btn {
      background-color: #4ecdc4;
      color: white;

      &:hover {
        background-color: #48bfb3;
        transform: translateY(-2px);
      }
    }
  }

  .content {
    display: flex;
    flex: 1;
    overflow: hidden;

    .diff-list {
      width: 400px;
      overflow-y: auto;
      border-right: 1px solid #ddd;
      background-color: #f9f9f9;
      padding: 15px;
      box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);

      h3 {
        margin-top: 0;
        padding-bottom: 10px;
        border-bottom: 1px solid #ddd;
        color: #333;
        font-size: 16px;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .diff-item {
        padding: 12px;
        margin-bottom: 10px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        background-color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        &:hover {
          background-color: #f0f0f0;
          transform: translateX(3px);
        }

        &.added {
          border-left: 4px solid #4caf50;
        }

        &.removed {
          border-left: 4px solid #f44336;
        }

        &.modified {
          border-left: 4px solid #ff9800;
        }

        .diff-location {
          font-size: 12px;
          color: #666;
          margin-bottom: 6px;
          font-weight: bold;
          .diff-title {
            margin-left: 10px;
          }
          .loading-title {
            margin-left: 10px;
          }
        }

        .diff-text {
          margin: 5px 0;
          word-break: break-word;
          line-height: 1.4;
          font-size: 14px;

          &.left-text {
            color: #f44336;
            background-color: rgba(244, 67, 54, 0.05);
            padding: 3px 5px;
            border-radius: 3px;
          }

          &.right-text {
            color: #4caf50;
            background-color: rgba(76, 175, 80, 0.05);
            padding: 3px 5px;
            border-radius: 3px;
          }
        }
      }

      .no-diffs {
        padding: 20px;
        text-align: center;
        color: #666;
        font-size: 14px;
      }
    }

    .pdf-container {
      flex: 1;
      overflow-y: auto;
      padding: 15px;
      position: relative;
      background-color: #f0f0f0;

      .pdf-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        color: #666;
        font-size: 16px;
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .page-wrapper {
        margin-bottom: 20px;
        position: relative;
        background-color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        overflow: hidden;
      }

      .pdf-loading,
      .page-loading {
        padding: 20px;
        text-align: center;
        color: #666;
      }
    }
  }

  .diff-tooltip {
    position: fixed;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    width: 300px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    z-index: 100;
    max-width: 400px;
    font-size: 13px;

    .tooltip-header {
      font-weight: bold;
      margin-bottom: 8px;
      border-bottom: 1px solid #eee;
      padding-bottom: 5px;
      color: #333;
    }

    .tooltip-section {
      margin-bottom: 10px;

      .tooltip-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 3px;
      }

      .tooltip-content {
        padding: 8px;
        background-color: #f9f9f9;
        border-radius: 4px;
        word-break: break-word;
        line-height: 1.4;

        &.left-content {
          color: #f44336;
          border-left: 3px solid #f44336;
        }

        &.right-content {
          color: #4caf50;
          border-left: 3px solid #4caf50;
        }
      }
    }
  }

  .processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    font-size: 16px;
    color: #333;

    .processing-spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #4ecdc4;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 波浪线高亮基础样式 */
.wave-underline-red {
  position: relative;
  display: inline-block;
  text-decoration: underline wavy red;
}

.wave-underline-green {
  position: relative;
  display: inline-block;
  text-decoration: underline wavy green;
}

/* 红色波浪线（左侧文档） */
.wave-underline-red::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 100%;
  height: 2px;
  background-size: 8px 2px;
}

/* 绿色波浪线（右侧文档） */
.wave-underline-green::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 100%;
  height: 2px;
  text-decoration: underline wavy green;
  background-size: 8px 2px;
}

/* 波浪动画 */
@keyframes waveMove {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 8px 0;
  }
}

/* 确保文本层与PDF页面尺寸一致 高亮样式*/
.text-layer {
  font-smooth: always;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform-origin: top left;
  z-index: 10;
}

.text-line {
  position: absolute;
  white-space: nowrap;
  transform-origin: top left;
}

.text-span {
  position: absolute;
  line-height: 1;
  transform-origin: top left;
  vertical-align: baseline !important;
}

.text-span.highlighted {
  background-color: rgba(255, 255, 0, 0.5) !important;
}

.table-line.highlighted {
  background-color: rgba(255, 255, 0, 0.5) !important;
  border: 1px solid yellow;
}

.tooltip-scrollable {
  overflow-y: auto;
  max-height: 350px;
  padding-right: 8px;
}

// 导出差异列表样式

.btnStyle {
  position: relative;
  right: 200px;
  top: -60px;
  text-align: right;
}
.btn {
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s;
  border: none;
  font-size: 14px;
}
.export-btn {
  background-color: #4caf50;
  color: white;
  margin-left: 15px;
  width: 150px;
  position: absolute;
}

.export-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
