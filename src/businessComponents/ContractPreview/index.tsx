/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-03 17:29:16
 * @Description: AI办公助手-场景-商务部合同比对-第二版
 */

import { useState, useEffect, useRef } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { Spin, message } from "antd";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "./index.less";
import { businessContractsComparisonAPI } from "@/api/businessContractsComparisonSecond";
import * as docx from "docx";
import { saveAs } from "file-saver";
import { handlePreprocessText } from "@/utils/common";

// console.log("version", pdfjs.version)
pdfjs.GlobalWorkerOptions.workerSrc =
  location.href.indexOf("copilot.sino-bridge.com") > -1
    ? "/toolbox/pdf.worker.min.js"
    : "/pdf.worker.min.js";

interface DiffItem {
  page: number;
  line: number;
  leftText: string;
  rightText: string;
  fullLeftText: string;
  fullRightText: string;
  type: "added" | "removed" | "modified";
  leftLineIndex?: number;
  rightLineIndex?: number;
  title?: string;
}

interface PdfTextContent {
  page: number;
  items: PdfTextItem[];
  lines: { text: string; items: PdfTextItem[] }[];
}

interface PdfTextItem {
  str: string;
  dir: string;
  transform: number[];
  width: number;
  height: number;
  fontName: string;
}

const ComparisonText: React.FC<{
  pdfA: string;
  pdfB: string;
  leftTextContent: PdfTextContent;
  rightTextContent: PdfTextContent;
}> = ({ pdfA, pdfB, leftTextContent, rightTextContent }) => {
  // console.log("leftTextContent", leftTextContent)
  // console.log("rightTextContent", rightTextContent)
  const [leftPdf, setLeftPdf] = useState<string | null>(null);
  const [rightPdf, setRightPdf] = useState<string | null>(null);
  const [numPagesLeft, setNumPagesLeft] = useState<number>(0);
  const [numPagesRight, setNumPagesRight] = useState<number>(0);
  const [diffs, setDiffs] = useState<DiffItem[]>([]);
  // const [leftTextContent, setLeftTextContent] = useState<PdfTextContent[]>([])
  // const [rightTextContent, setRightTextContent] = useState<PdfTextContent[]>([])
  const [hoveredDiff, setHoveredDiff] = useState<DiffItem | null>(null);
  const [, setMk] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [loadedTitleIndex, setLoadedTitleIndex] = useState(0);
  const [isPolling, setIsPolling] = useState<boolean>(true);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  // 点击高亮
  const [selectedDiff, setSelectedDiff] = useState<DiffItem | null>(null);

  const leftContainerRef = useRef<HTMLDivElement>(null);
  const rightContainerRef = useRef<HTMLDivElement>(null);
  const pageRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const [isScrolling, setIsScrolling] = useState(false); // 添加滚动状态标记位

  const batchSize = 10; // 每次请求的差异数量

  // 文档加载成功回调
  const onDocumentLoadSuccess = (isLeft: boolean) => (pdf: any) => {
    if (isLeft) {
      setNumPagesLeft(pdf.numPages);
    } else {
      setNumPagesRight(pdf.numPages);
    }
  };

  // 纯文本比较
  const handleFormatText = (text: string) => {
    return text
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "")
      .replace(
        /(的|和|等|之|与|及|或|而|且|但|然而|因此|所以|因为|由于|例如|比如|即|也就是说|换言之|简言之)/,
        ""
      );
  };

  // 计算对应字数出现的频率
  const handleTextsEquivalent = (text1: string, text2: string) => {
    // 1. 检查长度是否相同
    if (text1.length !== text2.length) return false;

    // 2. 创建字符频率计数器
    const createCharMap = (str) => {
      const map = {};
      for (let char of str) {
        map[char] = (map[char] || 0) + 1;
      }
      return map;
    };

    // 3. 比较字符频率
    const map1 = createCharMap(text1);
    const map2 = createCharMap(text2);

    // 4. 检查所有字符频率是否一致
    for (let char in map1) {
      if (map1[char] !== map2[char]) return false;
    }
    // console.log("map1", map1)
    // console.log("map2", map2)
    // console.log(
    //   "xiangfei",
    //   Object.keys(map1).length === Object.keys(map2).length
    // )
    return Object.keys(map1).length === Object.keys(map2).length;
  };

  // 比较文本差异（支持跨页匹配和整页检测）
  const compareTexts = (left: PdfTextContent[], right: PdfTextContent[]) => {
    const differences: DiffItem[] = [];
    const maxPages = Math.max(left.length, right.length);

    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      const leftPage = left.find((p) => p.page === pageNum);
      const rightPage = right.find((p) => p.page === pageNum);

      // 处理整页缺失的情况
      if (!leftPage && rightPage) {
        const pageLines = rightPage.lines;
        let displayText = "";
        if (pageLines.length >= 2) {
          displayText = `${pageLines[0].text}\n${pageLines[1].text}`;
        } else if (pageLines.length > 0) {
          displayText = pageLines[0].text;
        }

        differences.push({
          page: pageNum,
          line: 1,
          leftText: "",
          rightText: displayText,
          fullLeftText: "",
          fullRightText: pageLines.map((line) => line.text).join("\n"),
          type: "added",
          rightLineIndex: 0,
          rightLineIndexes: Array.from(
            { length: pageLines.length },
            (_, i) => i
          ),
        });
        continue;
      }

      if (leftPage && !rightPage) {
        const pageLines = leftPage.lines;
        let displayText = "";
        if (pageLines.length >= 2) {
          displayText = `${pageLines[0].text}\n${pageLines[1].text}`;
        } else if (pageLines.length > 0) {
          displayText = pageLines[0].text;
        }

        differences.push({
          page: pageNum,
          line: 1,
          leftText: displayText,
          rightText: "",
          fullLeftText: pageLines.map((line) => line.text).join("\n"),
          fullRightText: "",
          type: "removed",
          leftLineIndex: 0,
          leftLineIndexes: Array.from(
            { length: pageLines.length },
            (_, i) => i
          ),
        });
        continue;
      }

      if (!leftPage || !rightPage) continue;

      const leftLines = leftPage.lines;
      const rightLines = rightPage.lines;

      let leftIndex = 0;
      let rightIndex = 0;
      let lineNum = 1;

      // 收集当前页的删除行
      const removedLines: { text: string; index: number }[] = [];
      // 收集当前页的新增行
      const addedLines: { text: string; index: number }[] = [];
      // 收集修改行
      const modifiedLines: DiffItem[] = [];

      while (leftIndex < leftLines.length || rightIndex < rightLines.length) {
        const leftLine =
          leftIndex < leftLines.length
            ? handlePreprocessText(leftLines[leftIndex].text)
            : "";
        const rightLine =
          rightIndex < rightLines.length
            ? handlePreprocessText(rightLines[rightIndex].text)
            : "";

        // 直接匹配成功则跳过
        if (
          handleFormatText(leftLine) === handleFormatText(rightLine) ||
          handleTextsEquivalent(
            handleFormatText(leftLine),
            handleFormatText(rightLine)
          )
        ) {
          leftIndex++;
          rightIndex++;
          lineNum++;
          continue;
        }

        // 检查跨页匹配（跳过不视为差异）
        let skipLeft = false;
        let skipRight = false;

        if (leftLine) {
          const rightPrevPage = right.find((p) => p.page === pageNum - 1);
          const rightNextPage = right.find((p) => p.page === pageNum + 1);
          const existsInPrev = rightPrevPage?.lines.some(
            (l) => handlePreprocessText(l.text) === leftLine
          );
          const existsInNext = rightNextPage?.lines.some(
            (l) => handlePreprocessText(l.text) === leftLine
          );
          if (existsInPrev || existsInNext) {
            skipLeft = true;
          }
        }

        if (rightLine) {
          const leftPrevPage = left.find((p) => p.page === pageNum - 1);
          const leftNextPage = left.find((p) => p.page === pageNum + 1);
          const existsInPrev = leftPrevPage?.lines.some(
            (l) => handlePreprocessText(l.text) === rightLine
          );
          const existsInNext = leftNextPage?.lines.some(
            (l) => handlePreprocessText(l.text) === rightLine
          );
          if (existsInPrev || existsInNext) {
            skipRight = true;
          }
        }

        // 处理跨页匹配跳过逻辑
        if (skipLeft || skipRight) {
          if (skipLeft) {
            leftIndex++;
            lineNum++;
          }
          if (skipRight) {
            rightIndex++;
            lineNum++;
          }
          continue;
        }

        // 检查右侧后续行是否有匹配
        let foundRightMatch = false;
        for (
          let i = rightIndex + 1;
          i < Math.min(rightIndex + 5, rightLines.length);
          i++
        ) {
          if (leftLine === handlePreprocessText(rightLines[i].text)) {
            for (let j = rightIndex; j < i; j++) {
              addedLines.push({
                text: rightLines[j].text,
                index: j,
              });
              lineNum++;
            }
            rightIndex = i + 1;
            leftIndex++;
            foundRightMatch = true;
            break;
          }
        }
        if (foundRightMatch) continue;

        // 检查左侧后续行是否有匹配
        let foundLeftMatch = false;
        for (
          let i = leftIndex + 1;
          i < Math.min(leftIndex + 5, leftLines.length);
          i++
        ) {
          if (rightLine === handlePreprocessText(leftLines[i].text)) {
            for (let j = leftIndex; j < i; j++) {
              removedLines.push({
                text: leftLines[j].text,
                index: j,
              });
              lineNum++;
            }
            leftIndex = i + 1;
            rightIndex++;
            foundLeftMatch = true;
            break;
          }
        }
        if (foundLeftMatch) continue;

        // 处理单侧行存在的情况
        if (!leftLine && rightLine) {
          addedLines.push({
            text: rightLines[rightIndex].text,
            index: rightIndex,
          });
          rightIndex++;
          lineNum++;
          continue;
        }

        if (leftLine && !rightLine) {
          removedLines.push({
            text: leftLines[leftIndex].text,
            index: leftIndex,
          });
          leftIndex++;
          lineNum++;
          continue;
        }

        // 无匹配 → 记录为修改行
        if (leftLine && rightLine) {
          modifiedLines.push({
            page: pageNum,
            line: lineNum,
            leftText: handlePreprocessText(leftLines[leftIndex].text),
            rightText: handlePreprocessText(rightLines[rightIndex].text),
            fullLeftText: leftLines[leftIndex].text,
            fullRightText: rightLines[rightIndex].text,
            type: "modified",
            leftLineIndex: leftIndex,
            rightLineIndex: rightIndex,
          });
          leftIndex++;
          rightIndex++;
          lineNum++;
        }
      } // while 结束
      // console.log("differences", differences)
      setDiffs(differences); // 更新差异状态

      // 优化点：合并当前页的所有删除行为一个差异项
      if (removedLines.length > 0) {
        // 将所有删除行文本用换行符连接
        const fullText = removedLines.map((item) => item.text).join("\n");
        // 取前两行作为预览文本
        let previewText = "";
        if (removedLines.length >= 2) {
          previewText = `${removedLines[0].text}\n${removedLines[1].text}`;
        } else if (removedLines.length > 0) {
          previewText = removedLines[0].text;
        }

        differences.push({
          page: pageNum,
          line: removedLines[0].index + 1, // 使用第一个删除行的行号
          leftText: previewText,
          rightText: "",
          fullLeftText: fullText,
          fullRightText: "",
          type: "removed",
          leftLineIndexes: removedLines.map((item) => item.index),
        });
      }

      // 优化点：合并当前页的所有新增行为一个差异项
      if (addedLines.length > 0) {
        // console.log("addedLines", addedLines)
        // 将所有新增行文本用换行符连接
        const fullText = addedLines.map((item) => item.text).join("\n");
        // 取前两行作为预览文本
        let previewText = "";
        if (addedLines.length >= 2) {
          previewText = `${addedLines[0].text}\n${addedLines[1].text}`;
        } else if (addedLines.length > 0) {
          previewText = addedLines[0].text;
        }
        // console.log("previewText", previewText)

        differences.push({
          page: pageNum,
          line: addedLines[0].index + 1, // 使用第一个新增行的行号
          leftText: "",
          rightText: previewText,
          fullLeftText: "",
          fullRightText: fullText,
          type: "added",
          rightLineIndexes: addedLines.map((item) => item.index),
        });
      }
      // 添加独立修改行
      differences.push(...modifiedLines);
    }
  };

  // 滚动到差异位置（优化版）
  const handleScrollToDiff = (diff: DiffItem) => {
    if (isScrolling) return;
    setSelectedDiff(diff);
    setIsScrolling(true);

    const scrollToPosition = (
      container: HTMLDivElement | null,
      pageNum: number,
      lineIndex?: number,
      isLeft?: boolean
    ) => {
      if (!container || lineIndex === undefined) return;

      const pageElement = pageRefs.current[`page-${pageNum}`];
      if (!pageElement) return;

      // 查找文本项的位置信息
      const textContent = isLeft ? leftTextContent : rightTextContent;
      const pageData = textContent.find((p) => p.page === pageNum);
      if (!pageData || !pageData.lines[lineIndex]) return;

      let topPosition = pageElement.offsetTop;

      // 获取行内的第一个文本项来计算精确位置
      const textItems = pageData.lines[lineIndex].items;
      if (textItems.length > 0) {
        // 使用transform[5]作为垂直位置偏移
        const textTopOffset = textItems[0].transform[5];
        topPosition += textTopOffset;
      }

      // 滚动到目标位置（减去容器高度的1/3使其显示在视野中部）
      const containerHeight = container.clientHeight;
      const targetPosition = topPosition - containerHeight / 3;

      container.scrollTo({
        top: targetPosition,
        behavior: "smooth",
      });
    };

    // 左侧滚动处理
    if (diff.type === "removed" || diff.type === "modified") {
      // 对于合并的删除项，使用第一个行索引
      const lineIndex = diff.leftLineIndexes
        ? diff.leftLineIndexes[0]
        : diff.leftLineIndex;
      scrollToPosition(leftContainerRef.current, diff.page, lineIndex, true);
    }

    // 右侧滚动处理
    if (diff.type === "added" || diff.type === "modified") {
      // 对于合并的新增项，使用第一个行索引
      const lineIndex = diff.rightLineIndexes
        ? diff.rightLineIndexes[0]
        : diff.rightLineIndex;
      scrollToPosition(rightContainerRef.current, diff.page, lineIndex, false);
    }

    // 滚动完成后重置标记
    setTimeout(() => setIsScrolling(false), 500);
  };

  // 处理差异项悬停
  const handleDiffHover = (
    diff: DiffItem,
    e: React.MouseEvent<HTMLLIElement>
  ) => {
    setHoveredDiff(diff);
    setTooltipPosition({ x: e.clientX, y: e.clientY });
  };

  // 差异项悬浮离开
  const handleDiffLeave = () => {
    // setHoveredDiff(null)
  };

  // 添加选中状态的高亮
  const handleRenderCustomText = (
    textContent: PdfTextContent[],
    isLeft: boolean
  ) => {
    return textContent.map((page) => {
      // 检查当前页是否有选中的表格行
      const hasSelectedTableLine =
        selectedDiff &&
        selectedDiff.page === page.page &&
        page.lines.some(
          (line, lineIndex) =>
            line.isTable &&
            (isLeft
              ? selectedDiff.leftLineIndex === lineIndex ||
                (selectedDiff.leftLineIndexes &&
                  selectedDiff.leftLineIndexes.includes(lineIndex))
              : selectedDiff.rightLineIndex === lineIndex ||
                (selectedDiff.rightLineIndexes &&
                  selectedDiff.rightLineIndexes.includes(lineIndex)))
        );

      return (
        <div
          key={`text-layer-${page.page}-${isLeft ? "left" : "right"}`}
          className={`text-layer ${
            hasSelectedTableLine ? "highlighted-table" : ""
          }`}
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
          }}
        >
          {page.lines.map((line, lineIndex) => {
            // 检查当前行是否有差异
            const lineDiff = diffs.find(
              (diff) =>
                diff.page === page.page &&
                (isLeft
                  ? diff.leftLineIndex === lineIndex ||
                    (diff.leftLineIndexes &&
                      diff.leftLineIndexes.includes(lineIndex))
                  : diff.rightLineIndex === lineIndex ||
                    (diff.rightLineIndexes &&
                      diff.rightLineIndexes.includes(lineIndex)))
            );

            // 检查是否是选中的差异行
            const isSelectedLine =
              selectedDiff &&
              (isLeft
                ? selectedDiff.page === page.page &&
                  (selectedDiff.leftLineIndex === lineIndex ||
                    (selectedDiff.leftLineIndexes &&
                      selectedDiff.leftLineIndexes.includes(lineIndex)))
                : selectedDiff.page === page.page &&
                  (selectedDiff.rightLineIndex === lineIndex ||
                    (selectedDiff.rightLineIndexes &&
                      selectedDiff.rightLineIndexes.includes(lineIndex))));

            // 确定高亮样式
            let highlightClass = "";
            if (lineDiff) {
              if (
                isLeft &&
                (lineDiff.type === "removed" || lineDiff.type === "modified")
              ) {
                highlightClass = "wave-underline-red";
              } else if (
                !isLeft &&
                (lineDiff.type === "added" || lineDiff.type === "modified")
              ) {
                highlightClass = "wave-underline-green";
              }
            }

            // 表格行特殊处理
            if (line.isTable) {
              return (
                <div
                  key={`line-${lineIndex}`}
                  className={`table-line ${
                    isSelectedLine ? "highlighted" : ""
                  }`}
                  style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    fontSize: "0",
                    padding: "5px",
                  }}
                >
                  {line.text}
                </div>
              );
            }

            return (
              <div key={`line-${lineIndex}`} style={{ position: "absolute" }}>
                {line.items.map((item, itemIndex) => {
                  const transform = item.transform;
                  return (
                    <span
                      key={`item-${itemIndex}`}
                      className={`text-span ${highlightClass} ${
                        isSelectedLine ? "highlighted" : ""
                      }`}
                      style={{
                        position: "absolute",
                        left: `${transform[4]}px`,
                        top: `${transform[5]}px`,
                        fontSize: `${item.height}px`,
                        whiteSpace: "pre",
                        width: `${item.width}px`,
                        height: `${item.height}px`,
                        color: "transparent",
                      }}
                    >
                      {item.str}
                    </span>
                  );
                })}
              </div>
            );
          })}
        </div>
      );
    });
  };

  // 设置页面引用
  const handleSetPageRef =
    (pageNum: number) => (ref: HTMLDivElement | null) => {
      pageRefs.current[`page-${pageNum}`] = ref;
    };
  // 导出差异列表 handleExportDiffToDocx
  const handleExportDiffToDocx = () => {
    if (diffs.length === 0) {
      alert("没有差异可以导出");
      return;
    }

    // 创建DOCX文档结构
    const doc = new docx.Document({
      sections: [
        {
          properties: {},
          children: [
            new docx.Paragraph({
              text: "PDF差异报告",
              heading: docx.HeadingLevel.HEADING_1,
              alignment: docx.AlignmentType.CENTER,
              spacing: { after: 200 },
            }),
            new docx.Paragraph({
              text: `生成时间: ${new Date().toLocaleString()}`,
              alignment: docx.AlignmentType.CENTER,
            }),
            ...diffs.flatMap((diff, index) => [
              new docx.Paragraph({
                text: `差异 #${index + 1}`,
                heading: docx.HeadingLevel.HEADING_2,
                border: {
                  bottom: {
                    style: docx.BorderStyle.SINGLE,
                    size: 4,
                    color: "AAAAAA",
                  },
                },
              }),
              new docx.Paragraph({
                children: [
                  new docx.TextRun({
                    text: "位置: ",
                    bold: true,
                  }),
                  new docx.TextRun(`第${diff.page}页 第${diff.line}行`),
                ],
              }),
              new docx.Paragraph({
                children: [
                  new docx.TextRun({
                    text: "类型: ",
                    bold: true,
                  }),
                  new docx.TextRun({
                    text:
                      diff.type === "added"
                        ? "新增"
                        : diff.type === "removed"
                        ? "删除"
                        : "修改",
                    color:
                      diff.type === "added"
                        ? "00AA00"
                        : diff.type === "removed"
                        ? "AA0000"
                        : "0000AA",
                  }),
                ],
              }),
              ...(diff.type === "removed" || diff.type === "modified"
                ? [
                    new docx.Paragraph({
                      children: [
                        new docx.TextRun({
                          text: "左侧内容: ",
                          bold: true,
                        }),
                        new docx.TextRun(diff.fullLeftText),
                      ],
                    }),
                  ]
                : []),
              ...(diff.type === "added" || diff.type === "modified"
                ? [
                    new docx.Paragraph({
                      children: [
                        new docx.TextRun({
                          text: "右侧内容: ",
                          bold: true,
                        }),
                        new docx.TextRun(diff.fullRightText),
                      ],
                    }),
                  ]
                : []),
              new docx.Paragraph({}),
            ]),
          ],
        },
      ],
    });

    // 生成并下载文档
    docx.Packer.toBlob(doc).then((blob) => {
      saveAs(blob, "差异列表报告.docx");
    });
  };

  // 添加全局点击事件监听
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const tooltip = document.querySelector(".diff-tooltip");
      const diffItems = document.querySelectorAll(".diff-item");

      let isClickInsideTooltip = tooltip?.contains(event.target as Node);
      let isClickInsideDiffItem = false;

      diffItems.forEach((item) => {
        if (item.contains(event.target as Node)) {
          isClickInsideDiffItem = true;
        }
      });

      if (!isClickInsideTooltip && !isClickInsideDiffItem) {
        // setShowTooltip(false);
        setHoveredDiff(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // 加载PDF并提取文本
  useEffect(() => {
    // console.log("pdfA", pdfA)
    // console.log("pdfB", pdfB)

    if (pdfA) {
      setLeftPdf(pdfA);
    }
    if (pdfB) {
      setRightPdf(pdfB);
    }

    if (
      pdfA &&
      pdfB &&
      leftTextContent.length > 0 &&
      rightTextContent.length > 0
    ) {
      compareTexts(leftTextContent, rightTextContent);
    }
  }, [pdfA, pdfB, leftTextContent, rightTextContent]);

  // 滚动监听和分批加载逻辑
  useEffect(() => {
    if (diffs.length === 0) return;
    // const container = leftContainerRef.current
    // if (!container) return
    // return
    let intervalId: string | number | NodeJS.Timeout | undefined;
    // 生成差异title
    const handleGenerateTitle = async (diffBatch: DiffItem[]) => {
      // console.log('diffBatch', diffBatch)
      if (!diffBatch || diffBatch.length === 0) return;

      let resText = "";
      // console.log("diffBatch", diffBatch)
      // console.log("filteredDiffs", filteredDiffs)

      try {
        await businessContractsComparisonAPI(
          {
            files: diffBatch.map(
              ({ fullLeftText, fullRightText, page, line, type }) => ({
                fullLeftText,
                fullRightText,
                page,
                line,
                type,
              })
            ),
          },
          {
            onMessage: (text: string | null, finished: boolean) => {
              if (text) {
                const reg = /^```markdown\s*|```$/g;
                resText += text || "";
                setMk(
                  (p) =>
                    p +
                    text
                      .replace(reg, "")
                      .replace(/<\/?think>/g, "")
                      .replace("markdown", "")
                );
              }
              if (finished) {
                try {
                  const arr = JSON.parse(resText) as DiffItem[];
                  // 更新差异列表中的标题
                  setDiffs((prevDiffs) => {
                    return prevDiffs.map((diff) => {
                      const updatedDiff = arr.find(
                        (d) =>
                          d.page === diff.page &&
                          d.line === diff.line &&
                          d.type === diff.type
                      );
                      return updatedDiff
                        ? { ...diff, title: updatedDiff.title }
                        : diff;
                    });
                  });
                  setIsLoading(false);

                  setLoadedTitleIndex((prev) => prev + batchSize);
                  // console.log('loadedTitleIndex', loadedTitleIndex)
                  // console.log("arr", arr)
                  // 当下标大于等于差异列表的长度，则停止轮询
                  if (loadedTitleIndex >= diffs.length) {
                    setIsPolling(false);
                  }
                } catch (error) {
                  console.error("解析标题失败:", error);
                } finally {
                  setIsLoading(false);
                }
              }
            },
            onError: () => {
              setIsLoading(false);
            },
            onFinish: () => {
              setIsLoading(false);
            },
          }
        );
      } catch (err: unknown) {
        setIsLoading(false);
        if (err instanceof Error) {
          message.error(err.message);
        } else {
          message.error("生成标题时发生错误");
        }
      }
    };

    // console.log('isPolling', isPolling)
    // console.log("loadedTitleIndex", loadedTitleIndex)
    // console.log("diffs", diffs)
    if (isPolling) {
      // 立即执行一次
      const initialBatch = diffs.slice(
        loadedTitleIndex,
        loadedTitleIndex + batchSize
      );
      // console.log("initialBatch", initialBatch)
      handleGenerateTitle(initialBatch);
      // 设置定时器
      intervalId = setInterval(handleGenerateTitle, 60000);
    }

    // 设置定时器，每30秒调用一次fetchData函数
    return () => {
      clearInterval(intervalId);
    };
  }, [diffs, isPolling]);

  return (
    <>
      <div className="btnStyle">
        <button
          className="btn export-btn"
          disabled={!(leftTextContent && rightTextContent)}
          onClick={handleExportDiffToDocx}
        >
          导出差异列表
        </button>
      </div>
      <div className="app-container">
        <Spin
          tip="文档解析时间较长，请耐心等待..."
          spinning={isLoading}
          fullscreen
          size="large"
        />
        {hoveredDiff && (
          <div
            className="diff-tooltip"
            style={{
              left: `${tooltipPosition.x + 15}px`,
              top: `${tooltipPosition.y + 15}px`,
              transform: `translate(${
                tooltipPosition.x + 300 > window.innerWidth ? "-100%" : "0"
              }, ${
                tooltipPosition.y + 300 > window.innerHeight ? "-100%" : "0"
              })`,
            }}
          >
            <div className="tooltip-header">完整差异内容</div>
            <div className="tooltip-scrollable">
              <div className="tooltip-section">
                <div className="tooltip-label">左侧内容:</div>
                <div className="tooltip-content left-content">
                  {hoveredDiff.fullLeftText}
                </div>
              </div>
              <div className="tooltip-section">
                <div className="tooltip-label">右侧内容:</div>
                <div className="tooltip-content right-content">
                  {hoveredDiff.fullRightText}
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="content">
          <div className="diff-list" id="myElement">
            <h3>差异列表</h3>
            <ul>
              {diffs
                .filter(
                  (diff) =>
                    (diff.type === "added" &&
                      diff.rightText &&
                      diff.rightText.trim() !== "") ||
                    (diff.type === "removed" &&
                      diff.leftText &&
                      diff.leftText.trim() !== "") ||
                    (diff.type === "modified" &&
                      ((diff.leftText && diff.leftText.trim() !== "") ||
                        (diff.rightText && diff.rightText.trim() !== "")))
                )
                .map((diff, index) => (
                  <li
                    key={index}
                    className={`diff-item ${diff.type} ${
                      selectedDiff === diff ? "selected" : ""
                    }`}
                    onClick={() => handleScrollToDiff(diff)}
                    onMouseEnter={(e) => handleDiffHover(diff, e)}
                    onMouseLeave={handleDiffLeave}
                  >
                    <div className="diff-location">
                      第{diff.page}页 第{diff.line}行
                    </div>
                    {diff.type === "added" && (
                      <div className="diff-text right-text">
                        新增: {diff.rightText}
                      </div>
                    )}
                    {diff.type === "removed" && (
                      <div className="diff-text left-text">
                        删除: {diff.leftText}
                      </div>
                    )}
                    {diff.type === "modified" && (
                      <>
                        {diff.leftText && (
                          <div className="diff-text left-text">
                            原内容: {diff.leftText}
                          </div>
                        )}
                        {diff.rightText && (
                          <div className="diff-text right-text">
                            新内容: {diff.rightText}
                          </div>
                        )}
                      </>
                    )}
                  </li>
                ))}
              {diffs.filter(
                (diff) =>
                  (diff.type === "added" &&
                    diff.rightText &&
                    diff.rightText.trim() !== "") ||
                  (diff.type === "removed" &&
                    diff.leftText &&
                    diff.leftText.trim() !== "") ||
                  (diff.type === "modified" &&
                    ((diff.leftText && diff.leftText.trim() !== "") ||
                      (diff.rightText && diff.rightText.trim() !== "")))
              ).length === 0 &&
                leftPdf &&
                rightPdf && <li className="no-diffs">未发现文本差异</li>}
            </ul>
          </div>

          <div className="pdf-container left-pdf" ref={leftContainerRef}>
            {leftPdf ? (
              <Document
                file={leftPdf}
                onLoadSuccess={onDocumentLoadSuccess(true)}
              >
                {Array.from({ length: numPagesLeft }, (_, index) => {
                  const pageData = leftTextContent.find(
                    (p) => p.page === index + 1
                  );
                  const pageSize = pageData?.pageSize || {
                    width: 210,
                    height: 297,
                  }; // 默认尺寸

                  return (
                    <div
                      key={`left-page-${index + 1}`}
                      className="page-wrapper"
                      ref={handleSetPageRef(index + 1)}
                      style={{ position: "relative" }}
                    >
                      <Page
                        pageNumber={index + 1}
                        width={pageSize.width}
                        height={pageSize.height}
                        renderAnnotationLayer={false}
                        renderTextLayer={false}
                      />
                      {handleRenderCustomText(
                        leftTextContent.filter((p) => p.page === index + 1),
                        true
                      )}
                    </div>
                  );
                })}
              </Document>
            ) : (
              <div className="pdf-placeholder">请上传左侧PDF</div>
            )}
          </div>

          <div className="pdf-container right-pdf" ref={rightContainerRef}>
            {rightPdf ? (
              <Document
                file={rightPdf}
                onLoadSuccess={onDocumentLoadSuccess(false)}
              >
                {Array.from({ length: numPagesRight }, (_, index) => {
                  const pageData = rightTextContent.find(
                    (p) => p.page === index + 1
                  );
                  const pageSize = pageData?.pageSize || {
                    width: 210,
                    height: 297,
                  }; // 默认尺寸

                  return (
                    <div
                      key={`right-page-${index + 1}`}
                      className="page-wrapper"
                      ref={handleSetPageRef(index + 1)}
                      style={{ position: "relative" }}
                    >
                      <Page
                        pageNumber={index + 1}
                        width={pageSize.width}
                        height={pageSize.height}
                        renderAnnotationLayer={false}
                        renderTextLayer={false}
                      />
                      {handleRenderCustomText(
                        rightTextContent.filter((p) => p.page === index + 1),
                        false
                      )}
                    </div>
                  );
                })}
              </Document>
            ) : (
              <div className="pdf-placeholder">请上传右侧PDF</div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ComparisonText;
