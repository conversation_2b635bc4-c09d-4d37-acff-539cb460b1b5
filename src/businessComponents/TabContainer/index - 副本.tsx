import React, { useState, useRef, useImperativeHandle, forwardRef, Suspense } from 'react';
import { Tabs } from 'antd';
import type { TabsProps } from 'antd';
import './index.less';

const { TabPane } = Tabs;

export interface TabItem {
  title: string;
  content: string;
  key: string;
  props?: any;
}

export interface TabContainerRef {
  // getActiveTab: () => string;
  // setActiveTab: (key: string) => void;
  // getTabData: () => any;
  // refreshTab: (key: string) => void;
}

interface TabContainerProps {
  tabs: TabItem[];
  currentStep: string;
  className?: string;
  defaultActiveKey?: string;
  onTabChange?: (activeKey: string, tab: TabItem) => void;
  onDataChange?: (data: any) => void;
  onStepComplete?: (stepData: any) => void;
}

const TabContainer = forwardRef<TabContainerRef, TabContainerProps>(({ 
  tabs, 
  currentStep, 
  className = '',
  defaultActiveKey,
  onTabChange,
  onDataChange,
  onStepComplete
}, ref) => {
  const [activeTab, setActiveTab] = useState(defaultActiveKey || tabs[0]?.key);
  const [tabData, setTabData] = useState<Record<string, any>>({});

  // 组件映射表 - 这里可以根据需要扩展更多组件
  const componentMap: Record<string, React.ComponentType<any>> = {
    'ResumePreview': (props: any) => {
      // 从props中获取文件数据
      const fileData = props?.fileData?.[0]; // 获取第一个文件
      const fileUrl = fileData?.url || fileData?.fileStr || '';
      
      return (
        <div className="resume-preview">
          {fileUrl ? (
            <embed
              style={{ width: '100%', height: '100%', minHeight: 'calc(100vh - 160px)' }}
              type='application/pdf'
              src={fileUrl + '#toolbar=0&navpanes=0&scrollbar=0'}
            />
          ) : (
            <div className="no-file">
              <h3>简历预览</h3>
              <p>请先上传简历文件</p>
            </div>
          )}
        </div>
      );
    },
    'ResumeAnalysis': (props: any) => {
      // 动态导入AnalysisModule组件
      const AnalysisModule = React.lazy(() => import('../AnalysisModule'));
      
      return (
        <Suspense fallback={<div>加载中...</div>}>
          <AnalysisModule {...props} />
        </Suspense>
      );
    },
    'JobMatching': (props: any) => {
      // 动态导入JobMatchingModule组件
      const JobMatchingModule = React.lazy(() => import('../JobMatchingModule'));
      
      return (
        <Suspense fallback={<div>加载中...</div>}>
          <JobMatchingModule {...props} />
        </Suspense>
      );
    },
    'QuestionGeneration': (props: any) => {
      return (
        <div className="question-generation">
          <h3>智能题目生成</h3>
          <div className="question-content">
            <p>基于简历分析，已生成相关面试题目</p>
            <p>题目类型：技术能力、团队协作、问题解决</p>
          </div>
        </div>
      );
    },
    'InterviewArrangement': () => (
      <div className="interview-arrangement">
        <h3>面试安排内容</h3>
        <p>这是面试安排的详细内容，包含时间安排、面试官配置等</p>
        <div className="arrangement-info">
          <p><strong>面试时间：</strong>2024年1月15日 14:00</p>
          <p><strong>面试官：</strong>张经理、李总监</p>
        </div>
      </div>
    ),
    'InterviewMeeting': () => (
      <div className="interview-meeting">
        <h3>面试会议内容</h3>
        <p>这是面试会议的详细内容，包含会议记录、评分等</p>
        <div className="meeting-info">
          <p><strong>面试时长：</strong>45分钟</p>
          <p><strong>综合评分：</strong>8.5分</p>
        </div>
      </div>
    ),
    'EvaluationReport': () => (
      <div className="evaluation-report">
        <h3>评估报告内容</h3>
        <p>这是评估报告的详细内容，包含综合评价、建议等</p>
        <div className="report-info">
          <p><strong>综合评价：</strong>优秀</p>
          <p><strong>录用建议：</strong>建议录用</p>
        </div>
      </div>
    ),
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    // getActiveTab: () => activeTab,
    // setActiveTab: (key: string) => {
    //   setActiveTab(key);
    //   const tab = tabs.find(t => t.key === key);
    //   if (tab && onTabChange) {
    //     onTabChange(key, tab);
    //   }
    // },
    // getTabData: () => tabData,
    // refreshTab: (key: string) => {
    //   // 刷新指定tab的数据
    //   console.log(`刷新tab: ${key}`);
    //   if (onDataChange) {
    //     onDataChange({ type: 'refresh', tabKey: key });
    //   }
    // }
  }), [activeTab, tabs, tabData, onTabChange, onDataChange]);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    const tab = tabs.find(t => t.key === key);
    if (tab && onTabChange) {
      onTabChange(key, tab);
    }
    
    // 模拟数据变化
    if (onDataChange) {
      onDataChange({ 
        type: 'tabChange', 
        tabKey: key, 
        step: currentStep,
        timestamp: Date.now()
      });
    }
  };

  const tabItems: TabsProps['items'] = tabs.map(tab => ({
    key: tab.key,
    label: tab.title,
    children: (
      <div className="tab-content">
        {componentMap[tab.content] ? (
          <div className="tab-component-wrapper">
            {React.createElement(componentMap[tab.content], {
              ...tab.props,
              onDataChange: (data: any) => {
                setTabData(prev => ({ ...prev, [tab.key]: data }));
                if (onDataChange) {
                  onDataChange({ 
                    type: 'componentData', 
                    tabKey: tab.key, 
                    data,
                    step: currentStep
                  });
                }
              },
              onComplete: (data: any) => {
                if (onStepComplete) {
                  onStepComplete({ 
                    tabKey: tab.key, 
                    data,
                    step: currentStep,
                    timestamp: Date.now()
                  });
                }
              }
            })}
          </div>
        ) : (
          <div className="tab-not-found">
            组件 {tab.content} 未找到
            <p>请在componentMap中添加对应的组件映射</p>
          </div>
        )}
      </div>
    )
  }));

  return (
    <div className={`tab-container ${className}`}>
      <Tabs 
        activeKey={activeTab} 
        onChange={handleTabChange}
        items={tabItems}
        className="dynamic-tabs"
        type="card"
        size="small"
      />
    </div>
  );
});

TabContainer.displayName = 'TabContainer';

export default TabContainer;
