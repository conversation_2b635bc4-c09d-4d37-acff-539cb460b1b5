.tab-container {
  width: 100%;
  height: 100%;
  
  .dynamic-tabs {
    height: 100%;
    
    .ant-tabs-content-holder {
      height: 100%;
    }
    
    .ant-tabs-tabpane {
      height: 100%;
      // padding: 16px;
    }
  }
  
  .tab-content {
    // padding: 16px;
    min-height: 400px;
    
    .tab-component-wrapper {
      height: 100%;
    }
    
    .tab-not-found {
      text-align: center;
      color: #999;
      padding: 40px 0;
    }
    
    .resume-preview,
    .resume-analysis,
    .job-matching,
    .question-generation,
    .interview-arrangement,
    .interview-meeting,
    .evaluation-report {
      padding: 16px;
      background: #fafafa;
      border-radius: 8px;
      min-height: 200px;
      
      h3 {
        margin: 0 0 16px 0;
        color: #1890ff;
        font-size: 16px;
        font-weight: 600;
      }
      
      p {
        margin: 0 0 12px 0;
        line-height: 1.6;
        color: #333;
      }
      
      .resume-info,
      .analysis-info,
      .matching-info,
      .question-info,
      .arrangement-info,
      .meeting-info,
      .report-info {
        margin-top: 16px;
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e8e8e8;
        
        p {
          margin: 0 0 8px 0;
          font-size: 14px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          strong {
            color: #1890ff;
            margin-right: 8px;
          }
        }
      }
    }
  }
  
  // ResumePreview 组件样式
  .resume-preview {
    height: 100%;
    
    .no-file {
      text-align: center;
      padding: 40px 0;
      color: #999;
      
      h3 {
        margin-bottom: 16px;
        color: #666;
      }
    }
    
    embed {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
    }
  }
}
