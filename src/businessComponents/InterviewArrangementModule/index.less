.interview-arrangement-module {
  padding: 24px;
  // background: #f5f5f5;
  // min-height: 100vh;
  height: 100%;
  overflow: auto;

  .meeting-success-card {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  // 顶部区域
  .header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    .title {
      font-size: 16px;
      color: #333;
      font-weight: 500;
    }

    .global-copy-btn {
      height: 40px;
      padding: 0 24px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
    }
  }

  // 会议基本信息
  .meeting-info {
    padding: 24px 32px;
    border-bottom: 1px solid #f0f0f0;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 80px;
        color: #666;
        font-size: 14px;
        flex-shrink: 0;
      }

      .value {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  // 各个信息区块
  .section {
    padding: 24px 32px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .section-title {
      font-size: 16px;
      color: #333;
      font-weight: 600;
      margin: 0 0 20px 0;
    }

    // 链接区域
    .link-section {
      margin-bottom: 20px;

      .link-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .label {
          width: 100px;
          color: #666;
          font-size: 14px;
          flex-shrink: 0;
        }

        .link-url {
          color: #1890ff;
          text-decoration: none;
          font-size: 14px;
          word-break: break-all;
          line-height: 1.4;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .button-group {
        display: flex;
        gap: 12px;
        margin-left: 100px;

        .copy-link-btn {
          border: 1px solid #d9d9d9;
          color: #666;
          background: white;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }

        .open-link-btn {
          border: 1px solid #d9d9d9;
          color: #666;
          background: white;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }
      }
    }

    // 密码区域
    .password-section {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .label {
        width: 100px;
        color: #666;
        font-size: 14px;
        flex-shrink: 0;
      }

      .password-box {
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        padding: 8px 12px;
        min-width: 200px;

        .password-text {
          color: #333;
          font-size: 14px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          margin-right: 8px;
        }

        .copy-icon {
          color: #999;
          cursor: pointer;
          font-size: 14px;
          transition: color 0.2s;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }

    // 会议ID区域
    .meeting-id-section {
      display: flex;
      align-items: center;

      .label {
        width: 100px;
        color: #666;
        font-size: 14px;
        flex-shrink: 0;
      }

      .value {
        color: #333;
        font-size: 14px;
        font-weight: 500;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
    }
  }


  .schedule-card,
  .meeting-card {
    margin-bottom: 24px;
    
    .ant-card-head-title {
      font-weight: 600;
      color: #1890ff;
    }
  }

  .schedule-card {
    .ant-form-item-label > label {
      font-weight: 500;
      color: #262626;
    }

    .ant-input,
    .ant-picker,
    .ant-input-number {
      font-size: 14px;
    }
  }

  .create-action {
    text-align: center;
    margin-top: 24px;

    .ant-btn {
      min-width: 140px;
    }
  }

  .meeting-info {
    .meeting-title {
      font-size: 18px;
      font-weight: 600;
      color: #262626;
    }

    .meeting-details {
      .detail-item {
        display: flex;
        margin-bottom: 12px;
        align-items: flex-start;

        .detail-label {
          flex: 0 0 100px;
          font-weight: 600;
          color: #595959;
          text-align: right;
          margin-right: 12px;
        }

        .detail-value {
          flex: 1;
          color: #262626;

          .meeting-link {
            color: #1890ff;
            word-break: break-all;
            margin-bottom: 8px;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }

    .ant-divider {
      margin: 16px 0;

      &.ant-divider-horizontal {
        &.ant-divider-with-text-left {
          &::before {
            width: 0;
          }
        }
      }
    }
  }

  .transcript-card {
    .ant-list-item-meta-title {
      margin-bottom: 4px;
      font-weight: 500;
    }
    
    .ant-list-item-meta-description {
      color: #666;
      font-size: 13px;
    }
    
    .ant-progress-text {
      font-weight: 600;
    }
  }
}
