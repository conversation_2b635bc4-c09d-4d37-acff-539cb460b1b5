import React, {forwardRef, useEffect, useState, useImperativeHandle} from 'react';
import { Button, message,Card,Form, Input, DatePicker,Divider,Space, Typography } from 'antd';
import { CopyOutlined, LinkOutlined, UserOutlined } from '@ant-design/icons';
import { copyText } from '@/utils/clipboard'
import dayjs from 'dayjs'
import './index.less';
import { intelligentInterview } from "@/api/intelligentInterview";

const { Text } = Typography

export interface InterviewArrangementModuleRef {
  getMentionsData: () => any
}
interface InterviewArrangementModuleProps {
  currentEchoData: any;
  sessionId: string;
  meetingScheduleData: any;
  globalLoading: boolean;
  setGlobalLoading: (loading: boolean) => void;
  
}
const InterviewArrangementModule = forwardRef<InterviewArrangementModuleRef, InterviewArrangementModuleProps>((
    {
      currentEchoData,
      sessionId,
      meetingScheduleData,
      globalLoading,
      setGlobalLoading
    },
    ref
  ) => {
    const [messageApi, contextHolder] = message.useMessage();
    const [form] = Form.useForm()
    const [scheduledTime, setScheduledTime] = useState<dayjs.Dayjs | null>(null)
    const [interviewerName, setInterviewerName] = useState('');
    const [meetingTitle, setMeetingTitle] = useState('');
    const [generating, setGenerating] = useState(false);
    // const [meetingUrl, setMeetingUrl] = useState('https://meet.polyv.net/login?channelId=6328237&type=attendee');
    const [meetingUrl, setMeetingUrl] = useState<any>('');
    const [candidateName, setCandidateName] = useState('');
    const [meetingData, setMeetingData] = useState<any>({});
// 模拟数据
  // const meetingData = {
  //   // title: 'qqq - 面试会议',
  //   // candidate: 'qqq',
  //   // interviewer: 'aaa',
  //   hostUrl: 'https://meet.polyv.net/login?channelid=6328237&type=host', // 主持人链接
  //   hostPassword: 'host123456', // 主持人密码
  //   attendeePassword: 'attend123', // 参会密码
  //   webinarId: '6328237',  //会议室id
  //   scheduledTime: '',   // 面试时间
  //   duration: 60 // 默认60分钟
  // };

  useEffect(() => {
    console.log(meetingScheduleData, 'meetingScheduleData')
    console.log(currentEchoData, 'currentEchoData')
    if (currentEchoData && Object.keys(currentEchoData).length > 0) {
      console.log('有回显数据------')
      setCandidateName(currentEchoData?.meetingData?.candidateName)
      setMeetingTitle(`${currentEchoData?.meetingData?.candidateName} - 面试会议`)
      setMeetingData(currentEchoData?.meetingData)
      setMeetingUrl(currentEchoData?.meetingData?.meetingUrl)
      setScheduledTime(dayjs(currentEchoData?.meetingData?.scheduledTime))
      // const { candidateName } = currentEchoData;
      // setCandidateName(candidateName || '候选人');
      // setMeetingTitle(`${candidateName || '候选人'} - 面试会议`);
    } else {
      console.log('没有回显数据------')
    }
    if(meetingScheduleData?.candidateName) {
      setCandidateName(meetingScheduleData?.candidateName)
      setMeetingTitle(`${meetingScheduleData?.candidateName} - 面试会议`)
    }
  }, [meetingScheduleData])

  // 复制功能
  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(() => {
      messageApi.success(`${type}已复制到剪贴板`);
    }).catch(() => {
      messageApi.error('复制失败，请手动复制');
    });
  };

  // 一键复制完整会议信息
  const copyFullMeetingInfo = async () => {
    if (!meetingUrl || !meetingData) {
      messageApi.error('会议信息不完整，无法复制')
      return
    }

    const meetingInfo = `主办方正在邀请您参加研讨会
主题：${meetingTitle}
主持人
点击链接直接加入：${meetingData?.hostUrl || meetingUrl}
登录密码: ${meetingData?.hostPassword || 'host123456'}

参会人
点击链接直接加入：${meetingUrl}
登录密码: ${meetingData?.attendeePassword || 'attend123'}

建议使用谷歌浏览器或手机微信打开此链接进会`

    try {
      const success = await copyText(meetingInfo)
      if (success) {
        messageApi.success('完整会议信息已复制到剪贴板')
      } else {
        messageApi.error('复制失败，请手动复制')
      }
    } catch (error) {
      console.error('复制完整会议信息时发生错误:', error)
      messageApi.error('复制失败，请手动复制')
    }
  }

  // 创建会议
  const handleCreateMeeting = async () => {
    if (!scheduledTime || !interviewerName.trim() || !meetingTitle.trim()) {
      messageApi.error('请填写完整的面试信息')
      return
    }
    // 先注掉会话id
    // if (!sessionId) {
    //   messageApi.error('会话ID不存在，请重新开始')
    //   return
    // }

    setGlobalLoading(true)

    try {
      const meetingInfo = {
        title: meetingTitle,
        candidateName,
        interviewerName: interviewerName.trim(),
        scheduledTime: scheduledTime.toISOString(),
        duration: 60 // 默认60分钟
      }

      const meeting = await intelligentInterview.createMeeting(meetingInfo)
      console.log(meeting, 'meeting会议内容')
      setMeetingUrl(meeting.meetingUrl)
      setGlobalLoading(false)

      // 保存会议数据到状态
      setMeetingData({
        hostUrl: meeting.hostUrl,
        webinarId: meeting.webinarId,
        hostPassword: meeting.hostPassword,
        attendeePassword: meeting.attendeePassword,
        scheduledTime: meetingInfo.scheduledTime,
        duration: meetingInfo.duration,
        meetingUrl: meeting.meetingUrl,
        meetingTitle: meetingInfo.title,
        interviewerName: meetingInfo.interviewerName,
        
      })

      // // 保存完整的面试数据到数据库
      // await intelligentInterview.saveInterviewData(sessionId, {
      //   meeting_url: meeting.meetingUrl,
      //   host_url: meeting.hostUrl,
      //   webinar_id: meeting.webinarId,
      //   host_password: meeting.hostPassword,
      //   attendee_password: meeting.attendeePassword,
      //   scheduled_time: meetingInfo.scheduledTime,
      //   duration: meetingInfo.duration,
      //   meeting_title: meetingInfo.title,
      //   interviewer_name: meetingInfo.interviewerName
      // })

      messageApi.success('面试会议创建成功！')
    } catch (error) {
      console.error('会议创建失败:', error)
      messageApi.error(`会议创建失败: ${(error as Error).message}`)
    }
  }
  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getMentionsData: () => {
      return {
        // outputReviewData: outputReviewData,
        // info: resumeAnalysisResult +  startData?.query,
        // jobMatchResult: jobMatchResult,
        // basicAnalysisResult: basicAnalysisResult,
        meetingData: meetingData,
      }
    },
  }))

  return (
    <div className="interview-arrangement-module">
      <Card title="面试安排" className="schedule-card">
        <Form form={form} layout="vertical">
          <Form.Item
            label="面试官姓名"
            required
          >
            <Input
              placeholder="请输入面试官姓名"
              value={interviewerName || meetingData?.interviewerName}
              onChange={(e) => setInterviewerName(e.target.value)}
              prefix={<UserOutlined />}
              size="large"
            />
          </Form.Item>



          <Form.Item
            label="会议标题"
            required
          >
            <Input
              placeholder="会议标题将自动生成"
              value={meetingTitle}
              onChange={(e) => setMeetingTitle(e.target.value)}
              disabled
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="面试时间"
            required
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm"
              value={scheduledTime}
              onChange={setScheduledTime}
              placeholder="请选择面试时间"
              style={{ width: '100%' }}
              size="large"
              disabledDate={(current) => {
                // 禁用今天之前的日期
                return current && current < dayjs().startOf('day')
              }}
              disabledTime={(current) => {
                // 如果选择的是今天，禁用当前时间之前的时间
                if (current && current.isSame(dayjs(), 'day')) {
                  const now = dayjs()
                  return {
                    disabledHours: () => {
                      const hours = []
                      for (let i = 0; i < now.hour(); i++) {
                        hours.push(i)
                      }
                      return hours
                    },
                    disabledMinutes: (selectedHour: number) => {
                      if (selectedHour === now.hour()) {
                        const minutes = []
                        for (let i = 0; i <= now.minute(); i++) {
                          minutes.push(i)
                        }
                        return minutes
                      }
                      return []
                    }
                  }
                }
                return {}
              }}
            />
          </Form.Item>
        </Form>

        <div className="create-action">
          <Button
            type="primary"
            size="large"
            onClick={handleCreateMeeting}
            disabled={!interviewerName || !meetingTitle || !scheduledTime || generating}
            // loading={generating}
            // icon={<CalendarOutlined />}
          >
            创建面试会议
          </Button>
        </div>
      </Card>
      
      {meetingUrl && (
        <Card 
          title={
            <Space>
              <LinkOutlined />
              <span>面试会议信息</span>
            </Space>
          }
          className="meeting-card"
        >
          <div className="meeting-info">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <div className="meeting-title">面试会议已创建成功</div>
              <Button
                type="primary"
                icon={<CopyOutlined />}
                onClick={copyFullMeetingInfo}
                size="large"
              >
                一键复制会议信息
              </Button>
            </div>
            <div className="meeting-details">
              <div className="detail-item">
                <span className="detail-label">会议标题：</span>
                <span className="detail-value">{meetingTitle}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">候选人：</span>
                <span className="detail-value">{candidateName}</span>
              </div>
              <div className="detail-item">
                <span className="detail-label">面试官：</span>
                <span className="detail-value">{interviewerName}</span>
              </div>
              



              {/* 面试官信息 */}
              <Divider orientation="left">面试官信息</Divider>
              <div className="detail-item">
                <span className="detail-label">主持人链接：</span>
                <div className="detail-value">
                  <div className="meeting-link">{meetingData?.hostUrl || meetingUrl}</div>
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => copyToClipboard(meetingData?.hostUrl || meetingUrl, '主持人链接')}
                    style={{ marginTop: 8, marginRight: 8 }}
                  >
                    复制主持人链接
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    href={meetingData?.hostUrl || meetingUrl}
                    target="_blank"
                    style={{ marginTop: 8 }}
                  >
                    打开链接
                  </Button>
                </div>
              </div>
              {meetingData?.hostPassword && (
                <div className="detail-item">
                  <span className="detail-label">主持人密码：</span>
                  <div className="detail-value">
                    <Text code copyable>{meetingData.hostPassword}</Text>
                  </div>
                </div>
              )}

              {/* 候选人信息 */}
              <Divider orientation="left">候选人信息</Divider>
              <div className="detail-item">
                <span className="detail-label">参会链接：</span>
                <div className="detail-value">
                  <div className="meeting-link">{meetingUrl}</div>
                  <Button
                    type="default"
                    size="small"
                    onClick={() => copyToClipboard(meetingUrl, '参会链接')}
                    style={{ marginTop: 8, marginRight: 8 }}
                  >
                    复制参会链接
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    href={meetingUrl}
                    target="_blank"
                    style={{ marginTop: 8 }}
                  >
                    打开链接
                  </Button>
                </div>
              </div>
              {meetingData?.attendeePassword && (
                <div className="detail-item">
                  <span className="detail-label">参会密码：</span>
                  <div className="detail-value">
                    <Text code copyable>{meetingData.attendeePassword}</Text>
                  </div>
                </div>
              )}

              {/* 会议详情 */}
              {meetingData?.webinarId && (
                <div className="detail-item">
                  <span className="detail-label">会议ID：</span>
                  <div className="detail-value">
                    <Text code>{meetingData.webinarId}</Text>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* 会议信息预览 */}
          {/* <Divider orientation="left">会议邀请信息预览</Divider>
          <div style={{
            background: '#f6f8fa',
            border: '1px solid #d0d7de',
            borderRadius: 6,
            padding: 16,
            marginBottom: 16,
            fontFamily: 'monospace',
            fontSize: 13,
            lineHeight: 1.6,
            whiteSpace: 'pre-line'
          }}>
            {`主办方正在邀请您参加研讨会
主题：${meetingTitle}
主持人
点击链接直接加入：${meetingData?.hostUrl || meetingUrl}
登录密码: ${meetingData?.hostPassword || 'host123456'}

参会人
点击链接直接加入：${meetingUrl}
登录密码: ${meetingData?.attendeePassword || 'attend123'}

建议使用谷歌浏览器或手机微信打开此链接进会`}
          </div> */}

          {/* <Alert
            message="面试提醒"
            description="请将上方会议信息发送给候选人，并提醒其提前5-10分钟进入会议室进行设备调试。"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          /> */}
        </Card>
      )}
      
    </div>
  );
  })

InterviewArrangementModule.displayName = 'InterviewArrangementModule'

export default InterviewArrangementModule
