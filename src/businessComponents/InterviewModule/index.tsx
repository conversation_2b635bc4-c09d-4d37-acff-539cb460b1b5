// src/businessComponents/TabContainer/index.tsx
import React, { useState, useRef, useImperativeHandle, forwardRef, Suspense } from 'react'
import { Tabs } from 'antd'
import type { TabsProps } from 'antd'
import './index.less'

const { TabPane } = Tabs

// 这些组件移动到函数体外，确保引用稳定
const ResumePreview: React.FC<any> = (props) => {
  const fileData = props?.fileData?.[0]
  const fileUrl = fileData?.url || fileData?.fileStr || ''
  return (
    <div className="resume-preview">
      {fileUrl ? (
        <embed
          style={{ width: '100%', height: '100%', minHeight: 'calc(100vh - 160px)' }}
          type='application/pdf'
          src={fileUrl + '#toolbar=0&navpanes=0&scrollbar=0'}
        />
      ) : (
        <div className="no-file">
          <h3>简历预览</h3>
          <p>请先上传简历文件</p>
        </div>
      )}
    </div>
  )
}

const AnalysisModuleLazy = React.lazy(() => import('../AnalysisModule'))
const JobMatchingModuleLazy = React.lazy(() => import('../JobMatchingModule'))

const componentMap: Record<string, React.ComponentType<any>> = {
  ResumePreview,
  ResumeAnalysis: (props: any) => (
    <Suspense fallback={<div>加载中...</div>}>
      <AnalysisModuleLazy {...props} />
    </Suspense>
  ),
  JobMatching: (props: any) => (
    <Suspense fallback={<div>加载中...</div>}>
      <JobMatchingModuleLazy {...props} />
    </Suspense>
  ),
  QuestionGeneration: (props: any) => (
    <div className="question-generation">
      <h3>智能题目生成</h3>
      <div className="question-content">
        <p>基于简历分析，已生成相关面试题目</p>
        <p>题目类型：技术能力、团队协作、问题解决</p>
      </div>
    </div>
  ),
  InterviewArrangement: () => (/* ... */ <div />),
  InterviewMeeting: () => (/* ... */ <div />),
  EvaluationReport: () => (/* ... */ <div />),
}

const TabContainer = forwardRef<any, any>(({
  tabs,
  currentStep,
  className = '',
  defaultActiveKey,
  onTabChange,
  onDataChange,
  onStepComplete
}, ref) => {
  const [activeTab, setActiveTab] = useState(defaultActiveKey || tabs[0]?.key)
  // ...保持原有逻辑
  const tabItems: TabsProps['items'] = tabs.map(tab => ({
    key: tab.key,
    label: tab.title,
    children: (
      <div className="tab-content">
        {componentMap[tab.content] ? (
          <div className="tab-component-wrapper">
            {React.createElement(componentMap[tab.content], {
              ...tab.props,
              onDataChange: (data: any) => { /* ... */ },
              onComplete: (data: any) => { /* ... */ }
            })}
          </div>
        ) : (
          <div className="tab-not-found">组件 {tab.content} 未找到</div>
        )}
      </div>
    )
  }))

  return (
    <div className={`tab-container ${className}`}>
      <Tabs activeKey={activeTab} onChange={(k)=>{/*...*/}} items={tabItems} className="dynamic-tabs" type="card" size="small" />
    </div>
  )
})

export default React.memo(TabContainer)