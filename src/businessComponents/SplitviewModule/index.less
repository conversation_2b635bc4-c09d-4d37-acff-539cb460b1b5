.split-preview{
  .split-file-list{
    background: var(--ant-color-bg-container);
    padding: 0px 40px 0px 50px;
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      margin-bottom: 18px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .split-file-view{
    background: var(--ant-color-bg-container);
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .right{
    padding:0px 30px;
    background: var(--ant-color-bg-container);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.02),0px 1px 6px -1px rgba(0, 0, 0, 0.02),0px 1px 2px 0px rgba(0, 0, 0, 0.03);
  }
  .split-card{
    padding: 10px 20px 20px;
    height: 190px;
    .split-card-detail{
      padding: 0px;
    }
    .ant-card-head{
      padding: 0px;
      min-height: 42px !important;
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      line-height: 22px;
      color: #333333;
      border-bottom:0px;
    }
    .ant-card-body{
      padding: 0px;
      margin-top:var(--ant-margin-xs);
      font-size: var(--ant-font-size);
      line-height: var(--ant-line-height);
      color: #333333;
    }
    &:hover{
      border: 1px solid var(--ant-color-primary);
    }
  }
}