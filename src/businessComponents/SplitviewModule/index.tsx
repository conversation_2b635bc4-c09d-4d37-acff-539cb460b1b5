// 文档拆分
import {
  <PERSON><PERSON>,
  <PERSON>,
  Row,
  Tabs,
  Card,
  Flex,
  message,
  theme,
  Modal,
} from "antd";
import type { TabsProps } from "antd";
import { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import { splitFile } from "@/api/public";
import "./index.less";
import { ReadOutlined } from "@ant-design/icons";

interface MentionsComponentProps {
  setGlobalLoading?: (loading: boolean) => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  currentEchoData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  splitViewData: {
    originalFile: any; // 原始文件
    fileList: any[]; // 文件信息列表
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}
const { useToken } = theme;
const SplitPreviewModule = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      splitViewData = {
        files: [],
        originalFile: null,
        fileList: [],
        chunksData: [],
      },
      setGlobalLoading,
      onCallParent,
      currentEchoData,
    },
    ref
  ) => {
    const { token } = useToken();
    const [currentActiveKey, setCurrentActiveKey] = useState("0");
    const [chunksData, setChunksData] = useState(
      currentEchoData?.chunksData || []
    ); // 切分后的数据
    const [currentExtractModal, setCurrentExtractModal] = useState("1"); // 当前切分预览
    const [previewModal, setPreviewModal] = useState<{
      open: boolean;
      title: string;
      content: string;
    }>({ open: false, title: "", content: "" });
    // 添加文件切割方法
    const splitDataFiles = async (files: any, originalFile: any) => {
      if (!files[0].id || !originalFile) {
        message.error("没有可切割的模板文件");
        return [];
      }
      setGlobalLoading?.(true);
      try {
        const chunks: any = [];

        // 切割模板文件
        console.log("模板文件信息:", files);

        try {
          // 使用subwayReport.ts中的splitFile方法
          const fileChunks = await splitFile(originalFile);
          // 过滤掉content为空的chunk
          const filteredChunks = fileChunks?.chunkInfo?.filter(
            (chunk: any) => chunk.text && chunk.text.trim() !== ""
          );
          if (filteredChunks.length > 0) {
            chunks.push(...filteredChunks);
          } else {
            // message.warning(`文件 ${uploadedFile.name} 切割未完成，使用原始文件`)
          }
        } catch (splitError: any) {
          console.error(`切割文件 ${files[0].name} 失败:`, splitError);
          // message.warning(`文件 ${uploadedFile.name} 切割失败: ${splitError.message || '未知错误'}，将使用原始文件`)
        }
        setGlobalLoading?.(false);

        return chunks;
      } catch (error: any) {
        console.error("文件处理失败:", error);
        // message.error(`文件处理失败: ${error.message || '未知错误'}，将使用原始文件`)
        return [];
      }
    };

    useEffect(() => {
      if (chunksData && chunksData.length > 0) {
        setChunksData(chunksData);
      } else {
        // console.log(splitViewData.fileList, splitViewData.originalFile, 3444);
        // console.log(currentEchoData?.outputReview, 3242);
        // if (!currentEchoData?.outputReview || !currentEchoData?.inputReview) {
        //   getChunks();
        //   // splitDataFiles(splitViewData.fileList, splitViewData.originalFile)
        //   //   .then((chunks) => {
        //   //     setChunksData(chunks);
        //   //   })
        //   //   .catch((error) => {
        //   //     console.error("文件处理失败:", error);
        //   //   });
        // }
      }
    }, []);
    // 拿到切分的数据
    const getChunks = () => {
      console.log("切分");
      const chunks = [
        {
          id: "1",
          title: "标题1",
          text: "内容1",
          parentChain: "123123",
        },
        {
          id: "2",
          title: "标题2",
          text: "内容2",
          parentChain: "123123",
        },
      ];
      setChunksData(chunks);
      if (!currentEchoData?.outputReview) {
        onCallParent?.("输出", chunks);
      }
      console.log(23132131);
      // splitDataFiles(splitViewData.fileList, splitViewData.originalFile)
      //   .then((chunks) => {
      //     setChunksData(chunks);
      //     console.log(currentEchoData?.outputReview, 888811111);
      //     if (!currentEchoData?.outputReview) {
      //       onCallParent?.("输出", chunks);
      //     }
      //   })
      //   .catch((error) => {
      //     console.error("文件处理失败:", error);
      //   });
    };
    const tabItems: TabsProps["items"] = [
      {
        key: "1",
        label: "切分预览",
      },
    ];

    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          chunksData: chunksData,
        };
      },
      splitDataFiles: () => {
        console.log("到第一步了");
        getChunks();
      },
    }));
    return (
      <div style={{ height: "100%" }} className="split-preview">
        <Row style={{ height: "100%" }}>
          <Col md={12}>
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={setCurrentActiveKey}
              className="split-file-list"
              items={splitViewData?.fileList?.map((x, index) => ({
                key: index + "",
                label: x.name,
                children: (
                  <div
                    style={{
                      minHeight: "calc(100vh - 220px)",
                      overflowY: "auto",
                    }}
                  >
                    <embed
                      style={{
                        width: "100%",
                        minHeight: "calc(100vh - 220px)",
                      }}
                      type="application/pdf"
                      src={x.url + "#toolbar=0&navpanes=0&scrollbar=0"}
                    ></embed>
                  </div>
                ),
              }))}
            />
          </Col>
          <Col xs={24} md={12} className="right">
            <Tabs
              defaultActiveKey="1"
              items={tabItems}
              className="split-file-view"
              onChange={(e) => {
                setCurrentExtractModal(e);
              }}
              activeKey={currentExtractModal}
            />
            {currentExtractModal == "1" && (
              <Row
                gutter={[16, 16]}
                style={{
                  minHeight: "calc(100vh - 220px)",
                  overflowY: "auto",
                  paddingTop: "8px",
                }}
              >
                {chunksData.length > 0 &&
                  chunksData.map((x: any, index) => (
                    <Col span={12} key={index}>
                      <Card
                        title={x.title}
                        className="split-card"
                        extra={
                          <Button
                            type="link"
                            className="split-card-detail"
                            icon={<ReadOutlined />}
                            onClick={() =>
                              setPreviewModal({
                                open: true,
                                title: x.title,
                                content: x.text,
                              })
                            }
                          >
                            查看详情
                          </Button>
                        }
                        style={{ width: "100%" }}
                      >
                        <div
                          style={{
                            display: "-webkit-box",
                            WebkitLineClamp: 5,
                            WebkitBoxOrient: "vertical",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "normal",
                            minHeight: "110px",
                            lineHeight: token.lineHeight,
                          }}
                        >
                          {x.text}
                        </div>
                      </Card>
                    </Col>
                  ))}
              </Row>
            )}
          </Col>
        </Row>
        <Modal
          open={previewModal.open}
          title={previewModal.title}
          footer={null}
          onCancel={() =>
            setPreviewModal({ open: false, title: "", content: "" })
          }
          bodyStyle={{
            maxHeight: "70vh",
            minHeight: "500px",
            overflowY: "auto",
          }}
          style={{ top: "15vh", width: "60vw" }}
          width="70vw"
        >
          <div style={{ whiteSpace: "pre-wrap" }}>
            {(previewModal.content || "").replace(/\n{3,}/g, "\n\n")}
          </div>
        </Modal>
      </div>
    );
  }
);
// 添加 displayName
SplitPreviewModule.displayName = "SplitPreviewModule";
export default SplitPreviewModule;
