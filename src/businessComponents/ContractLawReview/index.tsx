import {
  Button,
  Col,
  Row,
  Tabs,
  Card,
  Checkbox,
  Form,
  List,
  Space,
  Tag,
  Modal,
  theme,
  Flex,
  Typography,
  Divider,
  Result,
} from "antd";
import {
  CheckCircleFilled,
  DownOutlined,
  ExclamationCircleFilled,
  FileTextOutlined,
  InfoCircleFilled,
  LeftOutlined,
  PlusOutlined,
  UpOutlined,
  WarningFilled,
} from "@ant-design/icons";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { getToken, getUserInfo } from "@/utils/auth";
import useSSEChat from "@/hooks/useSSEChat";
import StreamTypewriter from "@/component/StreamTypewriter";
import "./index.less";
import TabPane from "antd/es/tabs/TabPane";
interface FileItem {
  id: string;
  name: string;
  url: string;
}

interface MentionsComponentProps {
  agentId?: string;
  setGlobalLoading?: (loading: boolean) => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  currentEchoData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  pageInfo?: any; // 页面信息
  lawReviewData: {
    targentData: []; // 传过来文件拆分块后选中的数据
    rulesData: []; // 所有的规则
    fileList: []; // 文件信息
    ruleFrom: any; // 之前选择的视角等数据
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}

const riskLevelColors: Record<string, string> = {
  高风险: "red",
  中风险: "orange",
  低风险: "blue",
};
const { Title, Text } = Typography;
const { useToken } = theme;
const ContractLawReview = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      lawReviewData = {
        targentData: [],
        rulesData: [],
        ruleFrom: {},
        fileList: [],
      },
      setGlobalLoading,
      agentId,
      pageInfo,
      onCallParent,
      currentEchoData,
    },
    ref
  ) => {
    const sseChat = useSSEChat();
    const { token } = useToken();
    const [currentActiveKey, setCurrentActiveKey] = useState("0");
    const [showType, setShowType] = useState("全部");
    const [messageStr, setMessageStr] = useState<any>(""); // 返回的数据类型
    const [allData, setAllData] = useState<any>([]); // 所有的数据集合
    const items = [
      {
        key: "全部",
        label: <span> 全部（{allData.length}） </span>,
        children: null,
        // 可以在这里添加具体的高风险项目列表
        style: { backgroundColor: "#fff0f0" }, // 浅粉色背景
      },
      {
        key: "高风险",
        label: (
          <span>
            <ExclamationCircleFilled
              style={{ color: "#ff4d4f", marginRight: 8 }}
            />
            高风险（
            {allData.filter((x) => x.rule_level === "高风险").length}）
          </span>
        ),
        children: null,
        // 可以在这里添加具体的高风险项目列表
        style: { backgroundColor: "#fff0f0" }, // 浅粉色背景
      },
      {
        key: "中风险",
        label: (
          <span>
            <WarningFilled style={{ color: "#faad14", marginRight: 8 }} />
            中风险（
            {allData.filter((x) => x.rule_level === "中风险").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
      {
        key: "低风险",
        label: (
          <span>
            <InfoCircleFilled style={{ color: "#fadb14", marginRight: 8 }} />
            低风险（
            {allData.filter((x) => x.rule_level === "低风险").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
      {
        key: "已通过",
        label: (
          <span>
            <CheckCircleFilled style={{ color: "#52c41a", marginRight: 8 }} />
            已通过（
            {allData.filter((x) => x.rule_level === "已通过").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
    ];
    useEffect(() => {
      console.log(lawReviewData?.rulesData);
      console.log(lawReviewData?.targentData, 23442);
      if (lawReviewData?.rulesData && lawReviewData?.rulesData?.length > 0) {
        getContractData();
      }
    }, []);
    const getContractData = async () => {
      setMessageStr("");
      setGlobalLoading?.(true);

      // 拼接 str
      let str = "";
      lawReviewData?.targentData.forEach((item: any) => {
        str += `${item.title},${item.text}`;
      });

      const results: any[] = []; // 用来收集每次 getLawData 的结果

      for (let i = 0; i < lawReviewData.rulesData.length; i++) {
        if (i === 0 && !currentEchoData?.inputReview) {
          onCallParent?.(
            "输入",
            `${str},${lawReviewData?.rulesData[0]?.title}`
          );
        } else {
          const res: any = await getLawData(lawReviewData?.rulesData[i]?.title);
          const cleanRes = res
            .replace(/```json\s*|```$/g, "")
            .trim()
            .replace(/```/g, "")
            .trim();
          const jsonObject = JSON.parse(cleanRes)?.contract_review;
          results.push(jsonObject); // 👉 先收集起来
        }
      }

      // 👉 循环完统一更新
      if (results.length > 0) {
        const flatResults = results.flat();
        // setAllData((prev: any) => ({ ...prev, ...flatResults }));
        setAllData(flatResults);
        console.log(flatResults, 9999111);
        // 如果要拼接到 messageStr
        setMessageStr(`<listing>${JSON.stringify(flatResults)}</listing>`);
        if (!currentEchoData?.outputReview) {
          onCallParent?.("输出", JSON.stringify(flatResults));
        }
        setGlobalLoading?.(false);
      }
    };

    // 调取dify
    const getLawData = async (text: any) => {
      let str: any = "";
      lawReviewData?.targentData.forEach((item: any) => {
        str += `${item.title},${item.text}`; // 用反引号拼接
      });
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      return new Promise((resolve) => {
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },
          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId || "",
            agentId: agentId || "",
            path: "/chat-messages",
            difyJson: {
              inputs: {
                type: "规则审核",
              },
              pageinfo: pageInfo,
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: `${str},${text}`,
            },
          },
          query: {},
          onMessage: () => {},
          onFinished: (resultData: any) => {
            resolve(resultData); // 将结果传出去
          },
        });
      });
    };

    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          allData: allData,
        };
      },
      getContractData: () => {
        console.log("到第四步了");
        getContractData();
      },
    }));
    return (
      <div style={{ height: "100%" }} className="law-review">
        <Row style={{ height: "100%" }}>
          <Col xs={24} md={10}>
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={setCurrentActiveKey}
              className="law-file-list"
              items={lawReviewData?.fileList?.map((x, index) => ({
                key: index + "",
                label: x.name,
                children: (
                  <div style={{ minHeight: "calc(100vh - 160px)" }}>
                    <embed
                      style={{
                        width: "100%",
                        height: "100%",
                        minHeight: "calc(100vh - 160px)",
                      }}
                      type="application/pdf"
                      src={x.url + "#toolbar=0&navpanes=0&scrollbar=0"}
                    />
                  </div>
                ),
              }))}
            />
          </Col>

          <Col
            xs={24}
            md={14}
            className="law-right"
            style={{ height: "calc(100vh - 140px)" }}
          >
            <Flex className="law-right-header" gap={token.marginSM} vertical>
              <Flex justify="space-between" align="center">
                <Flex
                  style={{
                    fontSize: token.fontSizeXL,
                    fontWeight: "bold",
                    color: "#333",
                  }}
                  align="center"
                >
                  <Result status="success" />
                  审查结果
                </Flex>
                <Flex
                  style={{
                    fontSize: token.fontSize,
                    color: "#333333",
                    lineHeight: token.lineHeight,
                  }}
                  gap={token.marginMD}
                >
                  <span>
                    当前立场：
                    <span style={{ color: token.colorPrimary }}>
                      {lawReviewData?.ruleFrom?.stance || ""}
                    </span>
                  </span>
                  <span>
                    审查视角：
                    <span style={{ color: token.colorPrimary }}>
                      {lawReviewData?.ruleFrom?.angleSelectValue || ""}
                    </span>
                  </span>
                  <span>
                    审核模式：
                    <span style={{ color: token.colorPrimary }}>
                      {lawReviewData?.ruleFrom?.type_schema || ""}
                    </span>
                  </span>
                </Flex>
              </Flex>
              <Flex justify="space-between" align="center">
                <Flex
                  style={{
                    fontSize: token.fontSize,
                    color: "#333333",
                    lineHeight: token.lineHeight,
                    letterSpacing: "0.6px",
                    marginLeft: token.marginLG,
                  }}
                  gap={token.marginXS}
                >
                  共{allData.length}个风险点：高风险
                  {allData.filter((x) => x.rule_level === "高风险").length}
                  个，中风险
                  {allData.filter((x) => x.rule_level === "中风险").length}
                  个，低风险
                  {allData.filter((x) => x.rule_level === "低风险").length} 个
                </Flex>
              </Flex>
            </Flex>
            <Tabs
              defaultActiveKey="全部"
              activeKey={showType}
              onChange={(activeKey: string) => {
                setShowType(activeKey);
              }}
              items={items}
              className="law-file-view"
              tabBarStyle={{ marginBottom: 0 }}
              renderTabBar={(props, DefaultTabBar) => (
                <DefaultTabBar
                  {...props}
                  style={{ backgroundColor: "transparent" }}
                />
              )}
            />
            <div style={{ height: "calc(100vh - 320px)", overflow: "auto" }}>
              <StreamTypewriter
                text={messageStr}
                onchange={() => {
                  scroll();
                }}
                end={true}
                charsPerUpdate={5}
                components={{
                  listing({ children, className, ...props }: any) {
                    let isJSON = false;
                    let array: any = [];
                    console.log(children, 912);
                    try {
                      array = JSON.parse(children); // ✅ children 现在是合法 JSON
                      console.log(array, 888888888888);
                      isJSON = true;
                    } catch (error) {
                      console.log(3333321323);
                      isJSON = false;
                    }
                    const [expandedMap, setExpandedMap] = useState<
                      Record<number, boolean>
                    >({});

                    return isJSON ? (
                      <>
                        {array?.map((obj: any, index: number) => {
                          const expanded = expandedMap[index] ?? true; // 默认 true
                          return (
                            <>
                              {showType === obj?.rule_level ||
                              showType === "全部" ? (
                                <Card
                                  style={{ marginTop: "16px" }}
                                  className="law-contract-card"
                                  styles={{
                                    body: expanded
                                      ? { display: "none", padding: 0 }
                                      : {},
                                  }}
                                  title={
                                    <Flex
                                      justify="space-between"
                                      align="center"
                                    >
                                      <div
                                        style={{
                                          maxWidth: "calc(100% - 50px)",
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                          whiteSpace: "nowrap",
                                        }}
                                      >
                                        <Tag
                                          color={
                                            riskLevelColors[obj?.rule_level]
                                          }
                                        >
                                          {obj?.rule_level}
                                        </Tag>
                                        <Text>{obj?.rule_desc}</Text>
                                      </div>
                                      {expanded ? (
                                        <UpOutlined
                                          onClick={() =>
                                            setExpandedMap((prev) => ({
                                              ...prev,
                                              [index]: false,
                                            }))
                                          }
                                        />
                                      ) : (
                                        <DownOutlined
                                          onClick={() =>
                                            setExpandedMap((prev) => ({
                                              ...prev,
                                              [index]: true,
                                            }))
                                          }
                                        />
                                      )}
                                    </Flex>
                                  }
                                >
                                  {!expanded && (
                                    <>
                                      {/* 风险点标签页 */}
                                      <Tabs defaultActiveKey="1">
                                        {obj?.risk_points?.map(
                                          (item: any, index: any) => (
                                            <TabPane
                                              tab={`风险点${index + 1}`}
                                              key={index + 1}
                                            >
                                              {/* 合同条款内容 */}
                                              <Text
                                                style={{
                                                  whiteSpace: "pre-line",
                                                }}
                                              >
                                                {item?.original_text || "--"}
                                              </Text>

                                              {/* 校验结果区域 */}
                                              <p
                                                style={{
                                                  color: "#333",
                                                  fontWeight: "bold",
                                                  margin: "16px 0px",
                                                  fontSize: "16px",
                                                  lineHeight: "22px",
                                                }}
                                              >
                                                校验结果
                                              </p>
                                              <Space
                                                direction="vertical"
                                                size={16}
                                                style={{ width: "100%" }}
                                              >
                                                <Card
                                                  size="small"
                                                  className="tips-card"
                                                  style={{
                                                    background:
                                                      token.colorErrorBg,
                                                    border: "none",
                                                  }}
                                                >
                                                  <Text strong>风险提示：</Text>
                                                  <Text>
                                                    {item?.risk_warning || "--"}
                                                  </Text>
                                                </Card>

                                                <Card
                                                  size="small"
                                                  className="tips-card"
                                                  style={{
                                                    background:
                                                      token.colorPrimaryBg,
                                                    border: "none",
                                                  }}
                                                >
                                                  <Text strong>修改意见：</Text>
                                                  <Text>
                                                    {item?.suggestion || "--"}
                                                  </Text>
                                                </Card>
                                              </Space>
                                            </TabPane>
                                          )
                                        )}
                                      </Tabs>
                                    </>
                                  )}
                                </Card>
                              ) : null}
                            </>
                          );
                        })}
                      </>
                    ) : (
                      <code
                        {...props}
                        className={className}
                        style={{
                          wordWrap: "break-word",
                          wordBreak: "break-all",
                          overflowWrap: "break-word",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        {children}
                      </code>
                    );
                  },
                }}
              />
            </div>
          </Col>
        </Row>
      </div>
    );
  }
);
// 添加 displayName
ContractLawReview.displayName = "SplitPreviewModule";
export default ContractLawReview;
