import React, {
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from 'react'
import { VideoCameraOutlined } from '@ant-design/icons'
import { getToken, getUserInfo } from '@/utils/auth'
import { cacheGet } from '@/utils/cacheUtil'
import useSSEChat from '@/hooks/useSSEChat'
import TabContainer, { TabItem } from '../TabContainer'
import './index.less'


export interface VideoInterviewModuleRef {
  getMentionsData: () => any
}

interface VideoInterviewModuleProps {
  step: any
  globalLoading?: boolean
  setGlobalLoading?: (loading: boolean) => void
  leftTabs: any[],
  stepIndex: number,
  videoInterviewData: any,
}

const VideoInterviewModule = forwardRef<
  VideoInterviewModuleRef,
  VideoInterviewModuleProps
>(({ globalLoading, setGlobalLoading, leftTabs, stepIndex, videoInterviewData }, ref) => {
  const sseChat = useSSEChat()
  const [meetingData, setMeetingData] = useState<any>({})
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // 自动开始逻辑：当组件挂载且有开始数据时，根据当前步骤自动调用相应的接口
  useEffect(() => {
    if(videoInterviewData && Object.keys(videoInterviewData).length > 0) {
      console.log(videoInterviewData, 'videoInterviewData')
      setMeetingData(videoInterviewData)
    }
  }, [])


  // 统一的API接口调用方法
  const callUnifiedAPI = async (agentId: string) => {
    try {
      const tokenInfo = await getToken()
      const userInfo = await getUserInfo()
      const tenantId = cacheGet('tenantId')

      // 调用流式接口
      sseChat.start({
        url: '/dify/broker/agent/stream',
        headers: {
          'Content-Type': 'application/json',
          Token: tokenInfo || '',
        },
        body: {
          insId: '1',
          bizType: 'app:agent',
          bizId: agentId,
          agentId: agentId,
          path: '/chat-messages',
          // query: startData.query || '',
          query: '1',
          difyJson: {
            inputs: {
              Token: tokenInfo || '',
              tenantid: tenantId || '',
            },
            response_mode: 'streaming',
            user: userInfo?.id || 'anonymous',
            conversation_id: '',
            query: '1',
          },
        },
        query: {},
        message: '1',
        onMessage: (message) => {
          console.log(`接口返回消息:`, message)
          // 去掉 <think> 标签内容
          let cleanStr = message.replace(/<think>[\s\S]*?<\/think>/g, '').trim()

          try {
            // 提取 JSON
            const jsonMatch = cleanStr.match(/```(?:json)?\s*([\s\S]*?)```/)
            console.log('提取的JSON:', jsonMatch)
            if (jsonMatch) {
              const parsedData = JSON.parse(jsonMatch[1].trim())

              // 判断第一个JSON里有没有"评审结果"这一项
              if (parsedData['评审结果' as keyof typeof parsedData]) {
                // 如果有"评审结果"，说明是评审数据
                console.log('检测到评审结果:', parsedData)
                if (
                  parsedData['评审结果' as keyof typeof parsedData] === '异常'
                ) {
                  // setReviewData([parsedData])
                  // setReviewVisible(true)
                }
              }
            }
          } catch (e) {
            console.error('JSON 解析失败', e, cleanStr)
          }
          console.log('cleanStr:', cleanStr)
        },
        onFinished: (res) => {
          console.log(`接口完成:`, res)
          setGlobalLoading?.(false)
        },
      })
    } catch (error) {
      console.error(`接口调用失败:`, error)
      setGlobalLoading?.(false)
    }
  }
  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getMentionsData: () => {
      return {
      }
    },
  }))

  return (
    <div className="video-interview-module">
      <div className="interview-container">
        {/* 左侧动态Tab内容 */}
        <div className="left-section">

          {/* <Tabs
            items={items}
            className="dynamic-tabs"
            type="card"
            size="small"
          /> */}
          <TabContainer
            tabs={leftTabs}
            stepIndex={stepIndex || 0}
            className="left-tabs"
          />
        </div>

        {/* 右侧内容区域 */}
        <div className="right-section">
          <div className="meeting-content">
            <div className="component-wrapper">
              <div className="meeting-header">
                <h3>面试视频</h3>
                {/* <div className="meeting-info-icon">i</div> */}
              </div>

              <div
                className="meeting-form"
              >
                {/* <div className="logo-section">
                          <div className="meeting-logo">P</div>
                          <h2 className="meeting-title">qqq - 面试会议 - qqq面试</h2>
                        </div>
                        
                        <div className="form-fields">
                          <input 
                            type="text" 
                            placeholder="请输入昵称" 
                            className="meeting-input"
                          />
                          <input 
                            type="password" 
                            placeholder="请输入密码" 
                            className="meeting-input"
                          />
                          <button className="login-button">同意并登录</button>
                        </div>
                        
                        <div className="meeting-links">
                          <p className="agreement-text">
                            请仔细阅读
                            <a href="#" className="link">《服务协议》</a>
                            及
                            <a href="#" className="link">《隐私政策》</a>
                          </p>
                          <p className="browser-tip">推荐使用Chrome 74以上版本浏览器</p>
                        </div> */}



                {meetingData?.hostUrl ? (
                  <div className="iframe-container">
                    <iframe
                      ref={iframeRef}
                      src={String(meetingData.hostUrl)}
                      width="100%"
                      height="600"
                      style={{ border: 0 }}
                      allowFullScreen
                      allow="camera; microphone; fullscreen; display-capture; autoplay"
                      sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-presentation allow-top-navigation allow-downloads"
                      title="面试会议"
                    />
                  </div>
                ) : (
                  <div className="no-meeting">
                    <VideoCameraOutlined style={{ fontSize: 48, color: '#ccc' }} />
                    <p>暂无会议链接</p>
                  </div>
                )}
              </div>

              <div className="host-info">
                <h4>主持人登陆信息:</h4>
                <div style={{ display: 'flex', gap: '10px' }}>
                  <div className="info-item">
                    <span className="label">主持人密码：</span>
                    <span className="value">{meetingData.hostPassword || ''}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">参会密码：</span>
                    <span className="value">{meetingData.attendeePassword || ''}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">会议时长：</span>
                    <span className="value">{meetingData.duration || ''}分钟</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* <ReviewResultModal
          visible={reviewVisible}
          onCancel={() => setReviewVisible(false)}
          onContinue={() => {
            setIsType('是')
            setReviewVisible(false) // 关闭弹窗
            // runSequentially();
          }}
          reviewItems={reviewData}
          finalConclusion="不通过"
        /> */}
    </div>
  )
})

VideoInterviewModule.displayName = 'VideoInterviewModule'

export default VideoInterviewModule
