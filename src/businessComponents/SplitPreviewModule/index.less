.split-preview{
  position: relative;
  .split-preview-card{
    margin-bottom: var(--ant-margin-sm);
    .ant-card-body{
      padding: var(--ant-margin);
    }
  }
}
.rule-modal-style{
  .ant-tabs-nav{
    &::before {
      border-bottom:0px;
    }
  }
  .ant-modal-content{
    padding: 10px 30px 30px;
    .ant-form-item{
      label{
        font-size: var(--ant-font-size-lg);
        font-weight: 500;
        color: #333;
      }
      .ant-form-item-label{
        padding:0px;
      }
    }
    .title{
      font-size: var(--ant-font-size-lg);
      font-weight: 500;
      line-height: var(--ant-line-height-lg);
      letter-spacing: 0.59px;
      color: #333333;
    }
  }
}