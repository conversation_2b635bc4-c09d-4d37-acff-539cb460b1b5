import {
  <PERSON><PERSON>,
  Col,
  Row,
  Tabs,
  Card,
  Flex,
  Modal,
  Form,
  Select,
  Radio,
  message,
  Typography,
  theme,
  Tag,
} from "antd";
import {
  EyeOutlined,
  FileTextTwoTone,
  IdcardTwoTone,
  InsuranceTwoTone,
  UploadOutlined,
} from "@ant-design/icons";
import type { TabsProps } from "antd";
import { FileItem } from "./Step1";
import useSSEChat from "@/hooks/useSSEChat";
import { useLocation } from "react-router-dom";
import {
  useState,
  useMemo,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import ReviewResultModal from "@/component/ReviewResultModal";
import { getKnowledgeData } from "@/api/public";
import { extractContent } from "@/utils/common";
import { getToken, getUserInfo } from "@/utils/auth";
import { splitFile } from "@/api/public";
import TextArea from "antd/es/input/TextArea";
import "./index.less";

interface MentionsComponentProps {
  agentId?: string;
  setGlobalLoading?: (loading: boolean) => void;
  splitViewData: {
    messages: string;
    chunks: any[]; // 切分后的数据
    fileList: FileItem[]; // 文件列表
    isQuentially?: boolean; // 是否提取实体
    onDataReady?: (data: any) => void;
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}
// const appKey = import.meta.env["VITE_CONTRACT_SCENE_SET_TOKEN"] || "";
const { Text } = Typography;
const { useToken } = theme;
const SplitPreviewModule = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      splitViewData = { messages: "", fileList: [], chunks: [] },
      agentId,
      setGlobalLoading,
      isQuentially = false,
      onDataReady,
    },
    ref
  ) => {
    const sseChat = useSSEChat();
    const sseChatType = useSSEChat();
    const sseChatQuality = useSSEChat();
    const sseChatQangleView = useSSEChat();
    const { token } = useToken();
    const controagentId = "19d63db9-aca5-47ee-9ed2-64940155d058";
    const qualityAgentId = "1e468f0f-29e5-4003-bf65-40d47b94b955";
    const [currentActiveKey, setCurrentActiveKey] = useState("0");
    const [currentModal, setCurrentModal] = useState("1");
    const [isModalOpen, setIsModalOpen] = useState(true);
    const [form] = Form.useForm();
    const [workflowType, setWorkflowType] = useState("ContractLawReview"); // 选择模式
    const [libSelectValue, setLibSelectValue] = useState<string[]>([]);
    const [ruleDbType, setRuleDbType] = useState<string>("");
    const [options, setOptions] = useState<any[]>([]);
    const [params, setParams] = useState<any>({});
    const { search } = useLocation();
    const searchParams = useMemo(() => new URLSearchParams(search), [search]);
    const [knowledgeDataData, setKnowledgeDataData] = useState<any[]>([]);
    const [selectedStance, setSelectedStance] = useState("甲方"); // 甲方还是乙方
    const [angleSelect, setAngleSelect] = useState("1"); // 选择视角
    const ruleFormData = useRef<any>({}); // 所有form选中的数据
    const [keyPoints, setKeyPoints] = useState(`
      合同价格构成
      规则内容：合同总价必须明确标注为含税价或不含税价。如为含税价，须列明适用税率及税费金额；如为不含税价，须注明“不含税”字样并单独约定税费承担方。
      风险等级：高风险
      付款条件
      规则内容：付款节点必须绑定可验证的交付成果（如验收报告签署），付款周期严格限定为“甲方收到乙方开具的合格增值税专用发票后 Net 45天”。预付款比例不得超过合同总价20%，且需等额银行保函覆盖。
      风险等级：高风险
      价格调整限制
      规则内容：除国家强制性税费政策变更（需提供官方文件依据）外，合同履行期间禁止任何价格上调。原材料成本波动不得作为调价理由。
      风险等级：高风险
      发票合规要求
      规则内容：乙方必须提供增值税专用发票，发票项目须与合同标的完全一致。因乙方发票错误导致甲方税款损失的，乙方需赔偿实际税款损失并支付合同总价1%的违约金。
      风险等级：高风险
      履约担保
      规则内容：乙方需提供见索即付式独立履约保函，金额不低于合同总价10%，保函有效期覆盖至最终验收合格日+30天。开立银行限定为四大国有银行或上市股份制银行。
      风险等级：高风险
      审计权条款
      规则内容：甲方有权在合同履行期间及终止后3年内，审计乙方与合同相关的全部成本凭证及分包合同。若审计发现误差≥5%，审计费用由乙方承担。
      风险等级：高风险
      付款关联验收
      规则内容：任何一笔付款均以阶段验收合格为前提。最终10%款项必须作为质量保证金，释放时间不早于最终验收合格后12个月。
      风险等级：高风险
      违约责任（财务条款）
      规则内容：乙方延迟交付的违约金按日0.1%计算，累计上限不超过合同总价10%。因乙方原因导致甲方额外支出的成本（如另行采购价差），由乙方全额承担。
      风险等级：中风险
      跨境支付税务
      规则内容：涉及跨境付款时，合同中必须载明“本合同价格为税后净值”，所有代扣代缴税费（含增值税、所得税）由乙方承担。
      风险等级：中风险
      关联交易披露
      规则内容：若乙方为甲方关联方，合同须附集团转移定价报告及内部审批文件，价格不得高于非关联第三方报价。
      风险等级：中风险
      付款对象一致性
      规则内容：收款账户名称必须与合同签约主体完全一致，禁止第三方代收款项。付款凭证须标注合同编号及对应发票号。
      风险等级：低风险
      `); // 审核要点
    const [currentExtractModal, setCurrentExtractModal] = useState("1"); // 提取还是拆分
    const [reviewVisible, setReviewVisible] = useState<boolean>(false); // 输入评审弹框
    const [reviewData, setReviewData] = useState<any>([]); // 输入评审弹框数据
    // const [isType, setIsType] = useState<string>("否"); // 是否忽略错误
    const [contractType, setContractType] = useState<string>(""); // 合同类型
    const [parsedData, setParsedData] = useState<any>([]); // 提及的所有数据
    const reviewTitle = useRef<string>("输入信息评审");
    const isType = useRef<string>("否"); // 是否忽略错误
    const [isally, setIsally] = useState<boolean>(isQuentially); // 是否已经提取实体
    const [previewModal, setPreviewModal] = useState<{
      open: boolean;
      title: string;
      content: string;
    }>({ open: false, title: "", content: "" });
    useEffect(() => {
      setSelectedStance(form.getFieldValue("stance") || "");
    }, [form]);

    // 添加文件切割方法
    const splitDataFiles = async (files: any, originalFile: any) => {
      if (!files[0].id || !originalFile) {
        message.error("没有可切割的模板文件");
        return [];
      }

      try {
        const chunks: any = [];

        // 切割模板文件
        console.log("模板文件信息:", files);

        try {
          // 使用subwayReport.ts中的splitFile方法
          const fileChunks = await splitFile(originalFile);
          // 过滤掉content为空的chunk
          const filteredChunks = fileChunks?.chunkInfo?.filter(
            (chunk: any) => chunk.text && chunk.text.trim() !== ""
          );
          if (filteredChunks.length > 0) {
            chunks.push(...filteredChunks);
          } else {
            // message.warning(`文件 ${uploadedFile.name} 切割未完成，使用原始文件`)
          }
        } catch (splitError: any) {
          console.error(`切割文件 ${files[0].name} 失败:`, splitError);
          // message.warning(`文件 ${uploadedFile.name} 切割失败: ${splitError.message || '未知错误'}，将使用原始文件`)
        }

        return chunks;
      } catch (error: any) {
        console.error("文件处理失败:", error);
        // message.error(`文件处理失败: ${error.message || '未知错误'}，将使用原始文件`)
        return [];
      }
    };
    useEffect(() => {}, []);
    const tabItems: TabsProps["items"] = [
      {
        key: "1",
        label: "切分预览",
      },
      {
        key: "2",
        label: "实体提取",
      },
    ];
    const angleView = [
      {
        id: "1",
        icon: "/src/assets/images/splitPreview/fawu.png",
        iconActive: "/src/assets/images/splitPreview/fawuactive.png",
        name: "法务",
        desc: "关注合同条款合规性、风险控制及法律责任界定",
      },
      {
        id: "2",
        icon: "/src/assets/images/splitPreview/caiwu.png",
        iconActive: "/src/assets/images/splitPreview/caiwuactive.png",
        name: "财务",
        desc: "关注付款条件、发票要求及财务相关条款",
      },
      {
        id: "3",
        icon: "/src/assets/images/splitPreview/jishu.png",
        iconActive: "/src/assets/images/splitPreview/jishuactive.png",
        name: "技术",
        desc: "关注技术规范、知识产权及保密条款",
      },
      {
        id: "4",
        icon: "/src/assets/images/splitPreview/xiaoshou.png",
        iconActive: "/src/assets/images/splitPreview/xiaoshouactive.png",
        name: "销售",
        desc: "关注商务条款，交付条件及售后服务",
      },
      {
        id: "5",
        icon: "/src/assets/images/splitPreview/caigou.png",
        iconActive: "/src/assets/images/splitPreview/caigouactive.png",
        name: "采购",
        desc: "关注采购条款、供应商资质及质量保证",
      },
    ];
    const a = `1、文本块应代表【场景描述】的文本内容，如”合同法审“则需要判断该文本片段是否为合同内容；
    2、文本块内容开头和结尾应连贯，未发生切分异常情况（明显截断）；
    3、文本不应该包含大量乱码、难以解析的表格文本、混乱的修订痕迹/批注、或严重乱码，导致可读性极差。`;
    const b = `1、【当事人信息】： 合同各方名称/姓名、地址、联系方式、身份信息（如法人需统一社会信用代码、法定代表人；自然人需身份证号）。
    2、【合同标的】： 合同涉及的对象（如买卖的货物、租赁的房屋、提供的服务、转让的技术等）。
    3、【数量/范围】： 标的的具体数量或服务/权利的范围。
    4、【质量/标准】： 对标的物或服务质量的要求。
    5、【价款/报酬/对价】： 一方为获得标的或服务需支付的金钱或其他形式的对价。
    6、【履行期限、地点、方式】： 各方义务完成的时间、地点和具体方法。
    7、【违约责任】： 一方不履行或不当履行合同时应承担的责任（违约金、赔偿损失等）。
    8、【争议解决】： 发生纠纷时的处理途径（诉讼或仲裁，及管辖地）。
    9、【合同生效与终止】： 合同生效的条件和终止的情形。
    10、【不可抗力】： 定义及发生后的处理。
    11、【通知与送达】： 双方有效通信的方式和地址。`;
    const extract = async (text: string) => {
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      return new Promise((resolve) => {
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },
          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId || "",
            agentId: agentId || "",
            path: "/chat-messages",
            query: "1",
            difyJson: {
              inputs: {
                input_criteria: a,
                Output_standard: b,
                test_content: text,
                scence_description: "合同法审",
                type: isType.current,
              },
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: "1",
            },
          },
          query: {},
          message: "1",
          onFinished: (result: any) => {
            resolve(result); // 将结果传出去
          },
        });
      });
    };
    const handleOk = async () => {
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      form.validateFields().then(() => {
        const values = form.getFieldsValue();
        console.log(values, "values");
        console.log(libSelectValue, "libSelectValue");
        let angleSelectValue = "";
        angleView.forEach((item) => {
          if (item.id == form.getFieldValue("angleSelect")) {
            angleSelectValue = item.name;
          }
        });

        if (values.ruleDbType === "内部规则" && libSelectValue.length === 0) {
          message.error("请选择知识库");
          return;
        }
        let params = {
          type_schema: workflowType, // 模式分类
          contractType: contractType, // 合同类型
          standpoint: `${values.stance}${angleSelectValue}`, // 立场视角
          ruleDbType: "规则匹配", // 规则库类型
          key_points: values.keyPoints, // 审查要点
          Scene_type: "", // 场景类型
          type: "否", // 是否跳过输入评审
          input_criteria: `
          1、需要包含立场和视角，立场：甲方/乙方；视角：财务/法务/销售/技术/采购。
          2、需要包含提取要点。
          3、审查要点需要符合【场景描述要求】例如：场景描述为合同法审，则提取要点应符合该场景的需求`, // 输入标准
          RAG_Name: "", // 知识库名称
          scence_description: "合同法审", // 场景描述
        };
        ruleFormData.current = params;

        setIsModalOpen(false);
        sseChatQangleView.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },
          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: qualityAgentId || "",
            agentId: qualityAgentId || "",
            path: "/chat-messages",
            query: "1",
            difyJson: {
              inputs: params,
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: "1",
            },
          },
          query: {},
          message: "1",
          onMessage: () => {},
          onFinished: (result: any) => {
            console.log(result, 34);
            const matches = result.match(/```json([\s\S]*?)```/g);

            let arrayData: any[] = [];
            let objectData: any = {};

            if (matches) {
              matches.forEach((block) => {
                const jsonStr = block.replace(/```json|```/g, "").trim();
                try {
                  const parsed = JSON.parse(jsonStr);

                  if (Array.isArray(parsed)) {
                    arrayData = parsed; // 捕获数组
                  } else if (typeof parsed === "object" && parsed !== null) {
                    objectData = parsed; // 捕获对象
                  }
                } catch (e) {
                  console.error("JSON 解析失败:", e, jsonStr);
                }
              });
            }
            console.log(arrayData, "arrayData");
            console.log(objectData, "objectData");
            reviewTitle.current = "输出信息评审";
            setParsedData(arrayData);
            setReviewData([objectData]);
            setGlobalLoading?.(false);
          },
        });
        // onSetStep3Params(params);
        // onSetStep3Chunks(chunks);
        // onUpdateStep3Key(); // 调用onUpdateStep3Key来重置Step3组件状态
        // setParams(params);
      });
      // if (adjustmentType === "2") {
      //   // matchReviewChecklist();
      // }
    };
    // 合同质检
    const getQuality = async (arr: any) => {
      setGlobalLoading?.(true);
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      const text = arr.join(",");
      sseChatQuality.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: qualityAgentId || "",
          agentId: qualityAgentId || "",
          path: "/chat-messages",
          query: "1",
          difyJson: {
            inputs: {
              test_content: text,
              Output_standard: b,
            },
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: "1",
          },
        },
        query: {},
        message: "1",
        onMessage: () => {},
        onFinished: (result: any) => {
          console.log(result, 34);
          const matches = result.match(/```json([\s\S]*?)```/g);

          let arrayData: any[] = [];
          let objectData: any = {};

          if (matches) {
            matches.forEach((block) => {
              const jsonStr = block.replace(/```json|```/g, "").trim();
              try {
                const parsed = JSON.parse(jsonStr);

                if (Array.isArray(parsed)) {
                  arrayData = parsed; // 捕获数组
                } else if (typeof parsed === "object" && parsed !== null) {
                  objectData = parsed; // 捕获对象
                }
              } catch (e) {
                console.error("JSON 解析失败:", e, jsonStr);
              }
            });
          }
          console.log(arrayData, "arrayData");
          console.log(objectData, "objectData");
          reviewTitle.current = "输出信息评审";
          setParsedData(arrayData);
          setReviewData([objectData]);
          setGlobalLoading?.(false);
        },
      });
    };
    // 获取合同类型
    const getContractType = async () => {
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      sseChatType.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: controagentId || "",
          agentId: controagentId || "",
          path: "/chat-messages",
          query: "1",
          difyJson: {
            inputs: {
              txt_content: splitViewData?.chunks?.[0]?.text,
            },
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: "1",
          },
        },
        query: {},
        message: "1",
        onMessage: (result: any) => {
          const cleanStr = result
            .replace(/<think>[\s\S]*?<\/think>/g, "")
            .trim();
          setContractType(cleanStr);
        },
        onFinished: () => {},
      });
    };

    // 拿到实体提取的数据
    const runSequentially = async () => {
      setGlobalLoading?.(true);
      setCurrentExtractModal("2");
      getContractType(); // 获取合同类型
      if (!splitViewData?.chunks?.length) return;

      const arr: any[] = [];

      for (const item of splitViewData.chunks) {
        // 等待 extract 完成并拿到结果
        const res = await extract(item.text);
        arr.push(res);
      }
      const parsed = arr
        .filter((item) => item && item.trim()) // 去掉空字符串
        .map((item) => {
          // 去掉 <think> 标签内容
          const cleanStr = item.replace(/<think>[\s\S]*?<\/think>/g, "").trim();

          try {
            // 提取 JSON
            const jsonMatch = cleanStr.match(/```(?:json)?\s*([\s\S]*?)```/);
            if (jsonMatch) {
              return JSON.parse(jsonMatch[1].trim());
            }
          } catch (e) {
            console.error("JSON 解析失败", e, cleanStr);
          }
          return null;
        })
        .filter(Boolean); // 去掉解析失败的
      // 分组
      const errod = parsed.filter((item) => item["评审结果"] === "异常");
      if (errod && errod.length > 0) {
        reviewTitle.current = "输入信息评审";
        setReviewData(errod);
        setReviewVisible(true);
        setGlobalLoading?.(false);
        console.log(errod);
      } else {
        // 合同质检
        if (parsed && parsed.length < 1) {
          getQuality(arr);
        }
      }
      setIsally(true);
    };
    // useEffect(() => {
    //   // 提及
    //   runSequentially();
    //   // 合同类型
    //   getContractType();
    // }, [splitViewData?.chunks]);
    useEffect(() => {
      getKnowledgeData({
        pageNum: 1,
        pageSize: 999999,
        entity: {
          libName: "",
        },
      }).then((res) => {
        if (res.code === 200) {
          setLibSelectValue(params?.libIds?.split(",") || []);
          setKnowledgeDataData(res.data.records);
        }
      });
    }, []);
    const handleCancel = () => {
      form.resetFields();
      setIsModalOpen(false);
    };
    const showModal = (type: string) => {
      let defaultValue = "";
      if (type === "ContractLawReview") {
        const H1Str = extractContent(splitViewData.messages, "h1");
        let result = H1Str.split("：");
        if (result && result[1]) {
          let value = result[1].replace(/[^\w\u4e00-\u9fa5]/g, "");
          if (options.find((item) => item.value === value))
            defaultValue = value;
        }
      }
      setIsModalOpen(true);
      setWorkflowType(type);
      getKnowledgeData({
        pageNum: 1,
        pageSize: 999999,
        entity: {
          libName: "",
        },
      }).then((res) => {
        if (res.code === 200) {
          setLibSelectValue(params?.libIds?.split(",") || []);
          setKnowledgeDataData(res.data.records);
        }
      });
    };

    const handleSelectChange = (value: string[]) => {
      setLibSelectValue(value);
    };
    const items: TabsProps["items"] = [
      {
        key: "1",
        label: "选择审查身份",
      },
      {
        key: "2",
        label: "选择模式",
      },
    ];
    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          isQuentially: isally, // 是否提取
          reviewData: reviewData, // 提取结果，成功还是失败
          ruleFormData: ruleFormData.current, // form选中的数据
        };
      },
      getProcessedData: runSequentially, // 提取实体 // 暴露给父组件的方法
      showModal: showModal, // 暴露给父组件的方法
    }));
    return (
      <div
        style={{ padding: "0 20px 20px 20px", height: "100%" }}
        className="split-preview"
      >
        <Row gutter={48}>
          <Col xs={24} md={10}>
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={setCurrentActiveKey}
              items={splitViewData?.fileList?.map((x, index) => ({
                key: index + "",
                label: x.name,
                children: (
                  <div
                    style={{
                      minHeight: "calc(100vh - 340px)",
                      overflowY: "auto",
                    }}
                  >
                    <embed
                      style={{
                        width: "100%",
                        minHeight: "calc(100vh - 340px)",
                      }}
                      type="application/pdf"
                      src={x.url + "#toolbar=0&navpanes=0&scrollbar=0"}
                    ></embed>
                  </div>
                ),
              }))}
            />
          </Col>
          <Col xs={24} md={14} style={{ padding: "0px" }}>
            <Tabs
              defaultActiveKey="1"
              items={tabItems}
              onChange={(e) => {
                setCurrentExtractModal(e);
              }}
              activeKey={currentExtractModal}
            />
            {/* {currentExtractModal == "1" && (
              <Row
                gutter={[16, 16]}
                style={{ minHeight: "calc(100vh - 340px)", overflowY: "auto" }}
              >
                {splitViewData?.chunks.length > 0 &&
                  splitViewData?.chunks.map((x, index) => (
                    <Col span={12} key={index}>
                      <Card
                        hoverable
                        title={x.title}
                        extra={
                          <EyeOutlined
                            onClick={() =>
                              setPreviewModal({
                                open: true,
                                title: x.title,
                                content: x.text,
                              })
                            }
                          />
                        }
                        style={{ width: "100%" }}
                      >
                        <div
                          style={{
                            display: "-webkit-box",
                            WebkitLineClamp: 3,
                            WebkitBoxOrient: "vertical",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "normal",
                            minHeight: "60px",
                          }}
                        >
                          {x.text}
                        </div>
                      </Card>
                    </Col>
                  ))}
              </Row>
            )} */}

            {currentExtractModal == "2" && (
              <Row
                gutter={[16, 16]}
                style={{
                  width: "100%",
                  height: "calc(100vh - 340px)",
                  overflowY: "scroll",
                  marginLeft: "20px",
                }}
              >
                <Flex vertical style={{ flex: 1 }}>
                  <Flex
                    gap={token.marginLG}
                    style={{ position: "absolute", right: "0px", top: "15px" }}
                  >
                    {reviewData &&
                      reviewData.length > 0 &&
                      reviewTitle.current == "输出信息评审" && (
                        <Flex>
                          <span
                            style={{
                              color: "#f5222d",
                              fontSize: "14px",
                              fontWeight: "bold",
                            }}
                          >
                            输出评审：{reviewData?.[0]?.["评审结果"]}
                          </span>
                          <span
                            style={{
                              color: "#f5222d",
                              fontSize: "14px",
                              fontWeight: "bold",
                              marginLeft: "8px",
                              cursor: "pointer",
                            }}
                            onClick={() => {
                              setReviewVisible(true);
                            }}
                          >
                            点击查看详情
                          </span>
                        </Flex>
                      )}
                    <Flex gap={token.margin}>
                      <Flex
                        style={{ color: token.colorText, fontWeight: "bold" }}
                      >
                        合同类型:
                      </Flex>
                      <Flex style={{ color: token.colorTextSecondary }}>
                        {contractType}
                      </Flex>
                    </Flex>
                  </Flex>
                  <div style={{ marginTop: token.marginSM }}>
                    {parsedData.map((item, index) => {
                      const termTitle = Object.keys(item)[0];
                      const termDetails = item[termTitle];

                      let content;
                      if (typeof termDetails === "string") {
                        content = <Text>{termDetails}</Text>;
                      } else if (
                        typeof termDetails === "object" &&
                        termDetails !== null
                      ) {
                        content = Object.entries(termDetails).map(
                          ([key, value]) => (
                            <div key={key} style={{ marginBottom: "12px" }}>
                              <Text strong>{key}：</Text>
                              <Text>{String(value)}</Text>
                            </div>
                          )
                        );
                      } else {
                        content = (
                          <Text type="secondary">无法识别的数据格式</Text>
                        );
                      }

                      return (
                        <Card
                          key={index}
                          title={<Text strong>{termTitle}</Text>}
                          className="split-preview-card"
                          style={{
                            width: "100%",
                            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                            marginBottom: 16,
                          }}
                        >
                          {content}
                        </Card>
                      );
                    })}
                  </div>
                </Flex>
              </Row>
            )}
          </Col>
        </Row>
        <Modal
          open={previewModal.open}
          title={previewModal.title}
          footer={null}
          onCancel={() =>
            setPreviewModal({ open: false, title: "", content: "" })
          }
          bodyStyle={{
            maxHeight: "70vh",
            minHeight: "500px",
            overflowY: "auto",
          }}
          style={{ top: "15vh", width: "60vw" }}
          width="70vw"
        >
          <div style={{ whiteSpace: "pre-wrap" }}>
            {(previewModal.content || "").replace(/\n{3,}/g, "\n\n")}
          </div>
        </Modal>
        <Modal
          title=""
          maskClosable={false}
          open={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          width={880}
          className="rule-modal-style"
        >
          <Tabs
            defaultActiveKey="1"
            items={items}
            onChange={(e) => {
              setCurrentModal(e);
            }}
            activeKey={currentModal}
          />
          <Form
            layout="vertical"
            form={form}
            initialValues={{ layout: "vertical" }}
          >
            <Flex
              style={{ display: currentModal === "1" ? "flex" : "none" }}
              vertical
            >
              <Text
                style={{
                  fontSize: token.fontSize,
                  opacity: "0.6",
                  marginBottom: token.marginMD,
                }}
              >
                选择您在合同中的角色，系统将根据不同视角提供针对性的审查重点
              </Text>
              <Form.Item
                label="选择立场"
                name="stance"
                style={{ marginBottom: 24 }}
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
              >
                <Flex vertical>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      gap: 20,
                      marginTop: 12,
                    }}
                  >
                    {/* 我是甲方 */}
                    <div
                      onClick={() => {
                        form.setFieldsValue({ stance: "甲方" });
                        setSelectedStance("甲方");
                      }}
                      style={{
                        border:
                          selectedStance === "甲方"
                            ? "1px solid #1677ff"
                            : "1px solid #eee",
                        borderRadius: 8,
                        padding: "20px 20px 13px 20px",
                        cursor: "pointer",
                        background:
                          selectedStance === "甲方" ? "#e6f4ff" : "#fafbfc",
                        display: "flex",
                        justifyContent: "space-between",
                        gap: 12,
                      }}
                    >
                      <div>
                        <div style={{ fontWeight: 600 }}>我是甲方</div>
                        <div
                          style={{
                            fontSize: 12,
                            color: "#333333",
                            marginTop: "12px",
                            lineHeight: "12px",
                          }}
                        >
                          重点关注权益保障、违约责任等条款
                        </div>
                      </div>
                      <span>
                        <img
                          src="/src/assets/images/splitPreview/jiafang.png"
                          style={{ width: "39px", height: "58px" }}
                        />
                      </span>
                    </div>
                    {/* 我是乙方 */}
                    <div
                      onClick={() => {
                        form.setFieldsValue({ stance: "乙方" });
                        setSelectedStance("乙方");
                      }}
                      style={{
                        border:
                          selectedStance === "乙方"
                            ? "1px solid #1677ff"
                            : "1px solid #eee",
                        borderRadius: 8,
                        padding: "20px 20px 13px 20px",
                        cursor: "pointer",
                        background:
                          selectedStance === "乙方" ? "#e6f4ff" : "#fafbfc",
                        display: "flex",
                        justifyContent: "space-between",
                        gap: 12,
                      }}
                    >
                      <div>
                        <div style={{ fontWeight: 600 }}>我是乙方</div>
                        <div
                          style={{
                            fontSize: 12,
                            color: "#333333",
                            marginTop: "12px",
                            lineHeight: "12px",
                          }}
                        >
                          重点关注履约要求、付款条件等条款
                        </div>
                      </div>
                      <span>
                        <img
                          src="/src/assets/images/splitPreview/yifang.png"
                          style={{ width: "52px", height: "52px" }}
                        />
                      </span>
                    </div>
                  </div>
                </Flex>
              </Form.Item>
              <Form.Item
                label="选择视角"
                name="angleSelect"
                style={{ marginBottom: 24 }}
              >
                <Flex
                  align="center"
                  style={{
                    gap: 12,
                    marginTop: 10,
                  }}
                >
                  {angleView.map((item) => (
                    <Flex
                      onClick={() => {
                        form.setFieldsValue({ angleSelect: item.id });
                        setAngleSelect(item.id);
                      }}
                      vertical
                      style={{
                        border:
                          angleSelect === item.id
                            ? "1px solid #1677ff"
                            : "1px solid #eee",
                        borderRadius: 8,
                        padding: 20,
                        cursor: "pointer",
                        background:
                          angleSelect === item.id ? "#e6f4ff" : "#fafbfc",
                        display: "flex",
                        gap: 8,
                        width: 148, // 固定宽度
                        height: 174, // 固定高度
                      }}
                    >
                      <span style={{ fontSize: 28, color: "#1677ff" }}>
                        <img
                          src={item.icon}
                          style={{ width: 26, height: 26 }}
                        />
                      </span>
                      <div>
                        <Flex
                          style={{
                            fontWeight: 500,
                            fontSize: "16px",
                            color: "#333",
                          }}
                        >
                          {item.name}
                        </Flex>
                        <div
                          style={{
                            fontSize: 12,
                            lineHeight: "22px",
                            color: "#333",
                          }}
                        >
                          {item.desc}
                        </div>
                      </div>
                    </Flex>
                  ))}
                </Flex>
              </Form.Item>
              <Form.Item
                label="审查要点"
                name="keyPoints"
                rules={[{ required: true, message: "请输入审查要点" }]}
                style={{ marginBottom: 24, position: "relative" }}
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
              >
                <Button
                  type="link"
                  icon={<UploadOutlined />}
                  onClick={() => console.log("导入")}
                  style={{
                    position: "absolute",
                    top: "-40px", // 对齐标签顶部
                    right: 0, // 贴右
                    zIndex: 1, // 确保按钮在输入框上方
                  }}
                >
                  导入通用要点
                </Button>
                <TextArea
                  showCount
                  maxLength={20000}
                  value={keyPoints}
                  placeholder="请输入审查要点"
                  style={{ height: 120, resize: "none" }}
                  onChange={(e) => {
                    form.setFieldsValue({ keyPoints: e.target.value });
                    setKeyPoints(e.target.value);
                  }}
                />
              </Form.Item>
            </Flex>
            <Flex
              style={{ display: currentModal === "2" ? "flex" : "none" }}
              vertical
            >
              {/* 四宫格模式选择区 */}
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 16,
                  marginBottom: 24,
                }}
              >
                {/* 合同法审 */}
                <div
                  onClick={() => setWorkflowType("ContractLawReview")}
                  style={{
                    border:
                      workflowType === "ContractLawReview"
                        ? "2px solid #1677ff"
                        : "1px solid #eee",
                    borderRadius: 8,
                    padding: 20,
                    cursor: "pointer",
                    background:
                      workflowType === "ContractLawReview"
                        ? "#e6f4ff"
                        : "#fafbfc",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    gap: 20,
                  }}
                >
                  <div>
                    <div
                      style={{
                        fontWeight: 500,
                        fontSize: "16px",
                        color: "#333",
                      }}
                    >
                      合同法审
                    </div>
                    <div
                      style={{
                        fontSize: 12,
                        lineHeight: "22px",
                        color: "#333",
                        marginTop: "8px",
                      }}
                    >
                      智能合同审查与风险识别
                    </div>
                  </div>
                  <span>
                    <img
                      src="/src/assets/images/splitPreview/fashen.png"
                      style={{ width: 52, height: 52 }}
                    />
                  </span>
                </div>
                {/* 敏感词检出 */}
                <div
                  onClick={() => setWorkflowType("SensitiveWordCheck")}
                  style={{
                    border:
                      workflowType === "SensitiveWordCheck"
                        ? "1px solid #1677ff"
                        : "1px solid #eee",
                    borderRadius: 8,
                    padding: 16,
                    cursor: "pointer",
                    background:
                      workflowType === "SensitiveWordCheck"
                        ? "#e6f4ff"
                        : "#fafbfc",
                    display: "flex",
                    justifyContent: "space-between",
                    gap: 12,
                    alignItems: "center",
                  }}
                >
                  <div>
                    <div
                      style={{
                        fontWeight: 500,
                        fontSize: "16px",
                        color: "#333",
                      }}
                    >
                      敏感词校验
                    </div>
                    <div
                      style={{
                        fontSize: 12,
                        lineHeight: "22px",
                        color: "#333",
                        marginTop: "8px",
                      }}
                    >
                      文本敏感内容智能识别
                    </div>
                  </div>
                  <span>
                    <img
                      src="/src/assets/images/splitPreview/mingan.png"
                      style={{ width: 52, height: 52 }}
                    />
                  </span>
                </div>
              </div>
              {/* 原有Form表单内容 */}
              {workflowType === "ContractLawReview" ? (
                <>
                  {/* 四宫格模式选择区 */}
                  <Form.Item
                    label="规则类型"
                    name="ruleDbType"
                    rules={[{ required: true, message: "请选择规则库类型" }]}
                  >
                    <Radio.Group
                      options={[
                        { value: "内部规则", label: "内部规则" },
                        { value: "民法典", label: "民法典" },
                        { value: "AI智能生成", label: "AI智能生成" },
                      ]}
                      onChange={(e) => {
                        setRuleDbType(e.target.value);
                        setLibSelectValue([]);
                      }}
                    />
                  </Form.Item>
                  {ruleDbType === "内部规则" && (
                    <div>
                      <Select
                        mode="multiple"
                        style={{ width: "100%", marginTop: -20 }}
                        placeholder="请输入关键词筛选内部规则"
                        value={libSelectValue}
                        onChange={handleSelectChange}
                        showSearch
                        filterOption={(input, option) =>
                          option?.label
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                        options={knowledgeDataData.map((item) => ({
                          value: item.id,
                          label: item.libName,
                        }))}
                      />
                    </div>
                  )}
                </>
              ) : (
                <Form.Item label="敏感词规则库">
                  <Select
                    mode="multiple"
                    style={{ width: "100%", marginTop: -20 }}
                    placeholder="请选择内部规则"
                    value={libSelectValue}
                    onChange={handleSelectChange}
                    options={knowledgeDataData.map((item) => ({
                      value: item.id,
                      label: item.libName,
                    }))}
                  />
                </Form.Item>
              )}
            </Flex>
          </Form>
        </Modal>
        <ReviewResultModal
          visible={reviewVisible}
          onCancel={() => setReviewVisible(false)}
          onContinue={() => {
            isType.current = "是";
            setReviewVisible(false); // 关闭弹窗
            runSequentially();
          }}
          reviewItems={reviewData}
          title={reviewTitle.current}
        />
      </div>
    );
  }
);
// 添加 displayName
SplitPreviewModule.displayName = "SplitPreviewModule";
export default SplitPreviewModule;
