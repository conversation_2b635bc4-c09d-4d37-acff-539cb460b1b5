// router.tsx
import React, { lazy, Suspense } from "react";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { Spin } from "antd";
import { App } from "@/App";

const LazyLogin = lazy(() => import("@/pages/login"));
const LazyTool = lazy(() => import("@/pages/tool"));
const LazyTest = lazy(() => import("@/pages/test"));
const LazyInterview = lazy(() => import("@/pages/intelligent-interview")); // 确保路径没错
const LazyNotFound = lazy(() => import("@/pages/not-found")); // 确保路径没错
const LegalReview = lazy(() => import("@/pages/legalReview")); // 合同法审
const Legal = lazy(() => import("@/pages/legalReview-2")); // 合同法审

function Loading() {
  return (
    <div
      style={{
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Spin size="large" />
    </div>
  );
}

const router = createBrowserRouter(
  [
    {
      path: "/",
      element: <App />, // layout
      children: [
        {
          path: "/login",
          element: (
            <Suspense fallback={<Loading />}>
              <LazyLogin />
            </Suspense>
          ),
        },
        {
          path: "tool",
          element: (
            <Suspense fallback={<Loading />}>
              <LazyTool />
            </Suspense>
          ),
        },
        {
          path: "test",
          element: (
            <Suspense fallback={<Loading />}>
              <LazyTest />
            </Suspense>
          ),
        },
        {
          path: "legalReview",
          element: (
            <Suspense fallback={<Loading />}>
              <LegalReview />
            </Suspense>
          ),
        },
        {
          path: "legal",
          element: (
            <Suspense fallback={<Loading />}>
              <Legal />
            </Suspense>
          ),
        },
        {
          path: "intelligent-interview",
          element: (
            <Suspense fallback={<Loading />}>
              <LazyInterview />
            </Suspense>
          ),
        },
        {
          path: "*",
          element: (
            <Suspense fallback={<Loading />}>
              <LazyNotFound />
            </Suspense>
          ),
        },
      ],
    },
  ],
  { basename: "" }
);

export function Router() {
  return <RouterProvider router={router} />;
}
