// App.tsx
import { ConfigProvider } from "antd";
import zhCN from "antd/es/locale/zh_CN";
import themeToken from "../theme.json";
import { PermissionProvider } from "@/component/PermissionProvider";
import { Provider } from "react-redux";
import { GlobalProvider } from "@/component/Context";
import { store } from "@/store";
import { formConfig, getPopupContainerConfig } from "@/utils/ant";
import { Outlet } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store";
import { fetchConfig } from "@/store/Reducer/configReducer";
import { useEffect } from "react";
import "./index.less";

const InitConfig: React.FC = () => {
  const dispatch = useAppDispatch();
  const { data, loading } = useAppSelector((state) => state.configReducer);

  useEffect(() => {
    dispatch(
      fetchConfig(
        `${import.meta.env.VITE_USERINFO_BASS}/frontEndConfig/getConfig`
      )
    );
  }, [dispatch]);

  if (loading) return <div></div>;
  if (!data?.sso_domain) return <div>配置加载失败</div>;

  return null; // 不渲染 UI，只做初始化
};

export function App() {
  return (
    <ConfigProvider
      form={formConfig}
      getPopupContainer={getPopupContainerConfig}
      locale={zhCN}
      theme={themeToken}
    >
      <PermissionProvider>
        <GlobalProvider>
          <Provider store={store}>
            <InitConfig /> {/* 这里请求数据 */}
            <Outlet />
          </Provider>
        </GlobalProvider>
      </PermissionProvider>
    </ConfigProvider>
  );
}
