import request from "@/utils/request";

const VITE_API_BASE_DOC: string = import.meta.env["VITE_API_BASE_DOC"] || "";
export function getItemDetail(query: any): Promise<any> {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/doc/team/page`,
    method: "POST",
    data: query,
  });
}

// 查询最近的知识库
export function recentList(data: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/base/recent/list`,
    method: "POST",
    data,
  });
}

// 知识/知识库整体搜索
export function baseGetList(data: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/base/getList`,
    method: "POST",
    data,
  });
}
// 知识预览
export function onlinePreviewUrl(data: any) {
  return request({
    url: `${VITE_API_BASE_DOC}/knowledge/doc/onlinePreviewUrl?id=${data}`,
    method: "GET",
  });
}
