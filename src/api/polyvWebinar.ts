/**
 * 保利威视研讨会API接口
 */
import CryptoJS from 'crypto-js'

// 保利威视配置
const POLYV_USER_ID = import.meta.env.VITE_POLYV_USER_ID || ''
const POLYV_API_KEY = import.meta.env.VITE_POLYV_API_KEY || ''
const POLYV_SECRET = import.meta.env.VITE_POLYV_SECRET || ''
// 保利威视研讨会API基础地址 (使用新版V4接口，注意使用HTTP而不是HTTPS)
const POLYV_API_BASE = 'http://api.polyv.net/live/v4/channel' // 新版直播频道API地址

// 研讨会相关接口类型定义
export interface CreateWebinarRequest {
  title: string           // 研讨会标题
  startTime: string       // 开始时间 (ISO 8601格式)
  duration: number        // 持续时间（分钟）
  description?: string    // 描述
  maxParticipants?: number // 最大参与人数
  password?: string       // 会议密码
  settings?: {
    allowChat?: boolean    // 允许聊天
    allowRecord?: boolean  // 允许录制
    autoRecord?: boolean   // 自动录制
    requireApproval?: boolean // 需要审批
  }
}

export interface CreateWebinarResponse {
  webinarId: string      // 研讨会ID
  joinUrl: string        // 参会链接
  hostUrl: string        // 主持人链接
  password?: string      // 参会人密码
  hostPassword?: string  // 主持人密码
  status: string         // 状态
}

export interface WebinarInfo {
  webinarId: string
  title: string
  status: 'scheduled' | 'live' | 'ended'
  startTime: string
  duration: number
  participantCount: number
  recordingUrl?: string
  joinUrl: string
  hostUrl: string
}

export interface ParticipantInfo {
  email: string
  name: string
  role: 'host' | 'panelist' | 'attendee'
}

// 录制文件信息接口
export interface RecordFile {
  channelId: string
  fileId: string
  url: string
  startTime: string
  endTime: string
  fileSize: number
  duration: number
  bitrate: number
  resolution: string
  channelSessionId: string
  fileName: string
  daysLeft: number
  origin: string
  originSessionId: string
}

// 查询录制文件的请求参数
export interface GetRecordFilesRequest {
  channelId: string
  startDate?: string  // 格式：yyyy-MM-dd
  endDate?: string    // 格式：yyyy-MM-dd
  sessionId?: string  // 直播的场次ID
}

// 生成签名的工具函数 (严格按照保利威视官方签名规则)
function generateSign(params: Record<string, any>, secret: string): string {
  // 1. 过滤掉值为null或undefined的参数
  const filteredParams: Record<string, any> = {}
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
      filteredParams[key] = params[key]
    }
  })

  // 2. 按参数名字典排序（ASCII值大小升序）
  const sortedKeys = Object.keys(filteredParams).sort()

  // 3. 拼接参数字符串：key1value1key2value2...keyNvalueN （没有等号和&符号）
  const paramString = sortedKeys
    .map(key => `${key}${filteredParams[key]}`)
    .join('')

  // 4. 首尾加上appSecret：appSecret + 参数字符串 + appSecret
  const signString = secret + paramString + secret

  console.log('签名前字符串:', signString)

  // 5. 使用crypto-js生成MD5签名，转为大写
  const sign = CryptoJS.MD5(signString).toString().toUpperCase()

  console.log('签名后字符串:', sign)

  return sign
}

// 构建请求参数 (根据官方文档，只有 appId, timestamp 参与签名)
function buildRequestParams() {
  const timestamp = Date.now().toString() // 13位毫秒级时间戳

  // 只有这两个参数参与签名
  const signParams = {
    appId: POLYV_API_KEY,
    timestamp
  }

  const sign = generateSign(signParams, POLYV_SECRET)

  return {
    appId: POLYV_API_KEY,
    timestamp,
    sign
  }
}

// 保利威视研讨会API类
export class PolyvWebinarAPI {
  
  /**
   * 创建研讨会频道 (使用新版V4 API)
   */
  static async createWebinar(request: CreateWebinarRequest): Promise<CreateWebinarResponse> {
    try {
      // 构建URL参数 (只有这些参数参与签名)
      const urlParams = buildRequestParams()

      // 构建请求体参数 (根据新版API文档)
      const requestBody = {
        name: request.title, // 直播名称
        newScene: 'seminar', // 研讨会场景
        seminarHostPassword: 'host123456', // 主持人密码 (6-16位)
        seminarAttendeePassword: request.password || 'attend123', // 参会人密码 (6-16位)
        linkMicLimit: request.maxParticipants || 10, // 参会人数限制
        startTime: new Date(request.startTime).getTime() // 开始时间戳
      }

      // 构建完整的URL (按照官方测试API的参数顺序)
      const url = `${POLYV_API_BASE}/create?sign=${urlParams.sign}&timestamp=${urlParams.timestamp}&appId=${urlParams.appId}`

      console.log('请求URL:', url)
      console.log('请求体:', JSON.stringify(requestBody, null, 2))

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`HTTP error! status: ${response.status}, response: ${errorText}`)
      }

      const data = await response.json()

      console.log('保利威视API响应:', data)

      if (data.code !== 200 || !data.success) {
        const errorMsg = data.error?.desc || data.message || '创建研讨会失败'
        throw new Error(errorMsg)
      }

      // 根据实际API响应格式解析数据
      const channelId = data.data.channelId
      // 使用正确的保利威会议链接格式
      const hostUrl = `https://meet.polyv.net/login?channelId=${channelId}&type=host`
      const joinUrl = `https://meet.polyv.net/login?channelId=${channelId}&type=attendee`

      return {
        webinarId: channelId.toString(),
        joinUrl: joinUrl,
        hostUrl: hostUrl,
        password: data.data.seminarAttendeePassword || 'attend123', // 参会人密码
        hostPassword: data.data.seminarHostPassword || 'host123456', // 主持人密码
        status: 'created'
      }
    } catch (error) {
      console.error('创建研讨会失败:', error)
      throw new Error(`创建研讨会失败: ${(error as Error).message}`)
    }
  }

  /**
   * 获取研讨会信息 (暂时返回模拟数据)
   */
  static async getWebinarInfo(webinarId: string): Promise<WebinarInfo> {
    try {
      // 暂时返回模拟数据，后续可以根据保利威视的频道查询接口实现
      return {
        webinarId,
        title: '研讨会频道',
        status: 'scheduled',
        startTime: new Date().toISOString(),
        duration: 60,
        participantCount: 0,
        joinUrl: `https://live.polyv.cn/watch/${webinarId}`,
        hostUrl: `https://live.polyv.cn/admin/${webinarId}`
      }
    } catch (error) {
      console.error('获取研讨会信息失败:', error)
      throw new Error(`获取研讨会信息失败: ${(error as Error).message}`)
    }
  }

  /**
   * 邀请参与者 (暂时返回成功)
   */
  static async inviteParticipant(webinarId: string, participant: ParticipantInfo): Promise<boolean> {
    console.log('邀请参与者:', { webinarId, participant })
    // 暂时返回成功，后续可以根据保利威视的具体接口实现
    return true
  }

  /**
   * 删除研讨会 (暂时返回成功)
   */
  static async deleteWebinar(webinarId: string): Promise<boolean> {
    console.log('删除研讨会:', webinarId)
    // 暂时返回成功，后续可以根据保利威视的具体接口实现
    return true
  }

  /**
   * 获取频道录制视频信息
   */
  static async getRecordFiles(request: GetRecordFilesRequest): Promise<RecordFile[]> {
    try {
      const timestamp = Date.now()

      // 构建请求参数
      const params: Record<string, any> = {
        appId: POLYV_API_KEY,
        userId: POLYV_USER_ID,
        timestamp: timestamp.toString()
      }

      // 添加可选参数
      if (request.startDate) {
        params.startDate = request.startDate
      }
      if (request.endDate) {
        params.endDate = request.endDate
      }
      if (request.sessionId) {
        params.sessionId = request.sessionId
      }

      // 生成签名
      params.sign = generateSign(params, POLYV_SECRET)

      // 构建查询字符串
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')

      // 构建完整的API URL
      const apiUrl = `http://api.polyv.net/live/v2/channels/${request.channelId}/recordFiles?${queryString}`

      console.log('查询录制文件API URL:', apiUrl)

      // 发送请求
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log('查询录制文件API响应:', result)

      if (result.code === 200) {
        return result.data || []
      } else {
        throw new Error(result.message || '查询录制文件失败')
      }
    } catch (error) {
      console.error('查询录制文件失败:', error)
      throw error
    }
  }

  /**
   * 根据webinarId获取录制文件 (兼容旧接口)
   */
  static async getRecordings(webinarId: string): Promise<string[]> {
    try {
      // 这里webinarId实际上是channelId
      const recordFiles = await this.getRecordFiles({ channelId: webinarId })
      return recordFiles.map(file => file.url)
    } catch (error) {
      console.error('获取录制文件失败:', error)
      return []
    }
  }
}

// 测试签名算法的函数 (使用官方文档示例验证)
export function testSignature() {
  // 使用官方文档示例的参数进行测试
  const testParams = {
    appId: 'g4rqgmmjuo',
    channelIds: '2477096,2272655',
    endDay: '2022-06-18',
    startDay: '2022-05-20',
    timestamp: '1660270926732'
    // page: null, size: null 这些null值会被过滤掉
  }

  const testSecret = 'fsq2k5weced1h8vui657xtdva66whf0g' // 官方示例密钥
  const testSign = generateSign(testParams, testSecret)

  console.log('=== 官方示例签名测试 ===')
  console.log('参数:', testParams)
  console.log('生成的签名:', testSign)
  console.log('官方期望签名: 0D2BDA2FD04D93A2B8832B91FD973C4D')
  console.log('签名是否匹配:', testSign === '0D2BDA2FD04D93A2B8832B91FD973C4D')

  return testSign
}

// 测试API连接的简化函数
export async function testPolyvConnection() {
  try {
    console.log('测试保利威视API连接...')
    console.log('配置信息:', {
      userId: POLYV_USER_ID,
      appId: POLYV_API_KEY,
      apiBase: POLYV_API_BASE,
      secret: POLYV_SECRET ? '已配置' : '未配置'
    })

    // 创建一个简单的测试请求
    const testParams = buildRequestParams()

    console.log('请求参数:', testParams)

    // 测试签名算法
    testSignature()

    return {
      success: true,
      config: {
        userId: POLYV_USER_ID,
        appId: POLYV_API_KEY,
        apiBase: POLYV_API_BASE
      },
      params: testParams
    }
  } catch (error) {
    console.error('API连接测试失败:', error)
    return {
      success: false,
      error: (error as Error).message
    }
  }
}

export default PolyvWebinarAPI
