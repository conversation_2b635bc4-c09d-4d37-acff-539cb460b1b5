import { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Button, Card, Flex, Form, message, Spin, Typography } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { getResource, getUserInfoById, refreshGetToken } from "@/api/user";
import { cacheGet, cacheSet } from "@/utils/cacheUtil";
import { useIsDesktop } from "@/hooks/useIsDeskTop";
import loginMain from "@/assets/images/login/login-main.png";
import loginTip from "@/assets/images/login/login-tip.png";
import { navButtons } from "@/assets/config/menu";
import { usePermissions } from "@/component/PermissionProvider";
import IconFont from "@/component/IconFont";
import { useAppSelector } from "@/store";
import "./index.less";

const { Title } = Typography;

export default function Login() {
  const navigate = useNavigate();
  const { search } = useLocation();
  const searchParams = useMemo(() => new URLSearchParams(search), [search]);
  const tenantId = searchParams.get("tenantId") || "";
  const refreshToken = searchParams.get("refreshToken") || "";
  const { permissions, setPermissions } = usePermissions();
  const { data } = useAppSelector((state) => state.configReducer);
  const isDesktop = useIsDesktop();
  const [loading, setLoading] = useState<boolean>(false);
  const [token] = useState<string | null>(cacheGet("token"));
  const [tipContent, settipContent] = useState("加载中");

  useEffect(() => {
    if (cacheGet("token")) {
      const index = navButtons.findIndex((x) =>
        permissions.includes(x.permission || "")
      );
      if (index !== -1) {
        navigate(navButtons[index].url);
      } else {
        navigate("/tool");
      }
    }
  }, [permissions, navigate]);

  const handleNavigate = useCallback(
    (resourceList: string[]) => {
      if (resourceList.length > 0) {
        const index = navButtons.findIndex((x) =>
          resourceList.includes(x.permission || "")
        );
        if (index !== -1) {
          navigate(navButtons[index].url);
        }
      }
    },
    [navigate]
  );

  const getCurrentUser = useCallback(async () => {
    settipContent("获取用户信息...");
    const res = await getUserInfoById();
    if (res && res.code === 200) {
      const user = {
        id: res.data.id,
        nickName: res.data.nickName,
        position: res.data.position,
        mobile: res.data.mobile,
        gender: res.data.gender,
        email: res.data.email,
        bizMail: res.data.email,
        avatar: res.data.avatar,
        deptName: res.data.deptName,
        corpName: res.data.corpName,
        effectivePoint: res.data.baseEmployee?.effectivePoint,
        totalPoint: res.data.baseEmployee?.totalPoint,
      };
      cacheSet("userInfo", JSON.stringify(user));
    } else {
      message.error(res.msg);
    }
    settipContent("获取权限...");
    const resource = await getResource();
    if (resource && resource.code === 200) {
      cacheSet("permissions", JSON.stringify(resource.data.resourceList));
      setPermissions(resource.data.resourceList);
      handleNavigate(resource.data.resourceList);
    }
  }, [setPermissions, handleNavigate]);

  useEffect(() => {
    const getToken = async (refreshToken: string) => {
      settipContent("登录中...");
      const res = await refreshGetToken({ refreshToken });
      if (res && res.code === 200) {
        cacheSet("token", res.data.token);
        cacheSet("refreshTokenKey", res.data.refreshToken);
        await getCurrentUser();
      } else {
        message.error(res.msg);
      }
    };

    const handleMessage = async () => {
      if (!tenantId || !refreshToken) {
        return;
      }
      cacheSet("tenantId", tenantId);
      await getToken(refreshToken);
      setLoading(false);
      navigate("/tool");
    };

    handleMessage();
  }, [navigate, getCurrentUser, tenantId, refreshToken]);

  const handleLoginClick = (type: string) => {
    window.location.href = `${data.sso_domain}?fromType=web&operationType=${type}&redirect=${window.location.href}`;
  };

  return (
    <>
      {!token && !tenantId && !refreshToken ? (
        <Flex align="center" justify="center" className="login">
          {isDesktop && <div className="login-bg-1" />}
          <Card className="login-content" variant="borderless">
            <Title level={4} style={{ marginBottom: 25 }}>
              智能办公助手
            </Title>
            <div className="login-main-box">
              <div className="login-bg-bubble" />
              <img src={loginMain} className="login-main" />
              <img src={loginTip} className="login-tip" />
            </div>
            <Form name="login" initialValues={{ remember: true }}>
              <Form.Item style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  block
                  icon={<IconFont type="Login" className="icon" />}
                  onClick={() => handleLoginClick("login")}
                  loading={loading}
                >
                  登录
                </Button>
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <Button
                  type="default"
                  block
                  icon={<UserOutlined />}
                  onClick={() => handleLoginClick("register")}
                  loading={loading}
                >
                  注册
                </Button>
              </Form.Item>
            </Form>
          </Card>
          <Flex className="login-bg-footer" vertical gap={8}>
            <span>© 2024 智能办公助手 版权所有</span>
            <span> ICP 备案号：京ICP备2024010101号-1</span>
          </Flex>
        </Flex>
      ) : (
        <Spin spinning={true} tip={tipContent} fullscreen />
      )}
    </>
  );
}
