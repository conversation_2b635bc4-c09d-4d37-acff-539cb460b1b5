// DraftPage.tsx
import React, {
  useState,
  Suspense,
  useRef,
  createRef,
  RefObject,
  useEffect,
  useCallback,
} from "react";
import {
  Button,
  Flex,
  Form,
  message,
  Upload,
  Select,
  Spin,
  Typography,
  theme,
  Steps,
} from "antd";
import HeaderCom from "@/component/Header";
import uploadIcon from "@/assets/images/public/upload.png";
import { fileToBase64, extractJsonString } from "@/utils/common";
import useSSEChat from "@/hooks/useSSEChat";
import { convertFileToPDF, uploadChatFile } from "@/api/public";
import IconFont from "@/component/IconFont";
import { getToken, getUserInfo } from "@/utils/auth";
import ReviewResult from "@/component/ReviewResult";
import { cacheGet } from "@/utils/cacheUtil";
import "./index.less";
import { RedoOutlined } from "@ant-design/icons";
const { useToken } = theme;
const VITE_SCENE_INPUT: string = import.meta.env["VITE_SCENE_INPUT"] || ""; // 场景输入官
const VITE_SCENE_PUT: string = import.meta.env["VITE_SCENE_PUT"] || ""; // 场景输出官
const VITE_UNIT_INPUT: string = import.meta.env["VITE_UNIT_INPUT"] || ""; // 单元场景输入评审
const VITE_UNIT_OUTPUT: string = import.meta.env["VITE_UNIT_OUTPUT"] || ""; // 单元场景输出评审
const VITE_SCENE_PLANNING: string =
  import.meta.env["VITE_SCENE_PLANNING"] || ""; // 场景规划师

// 自动匹配业务组件
const modules = import.meta.glob("/src/businessComponents/**/index.tsx");

const DraftPage: React.FC = () => {
  const sseChat = useSSEChat();
  const { token } = useToken();
  const [pageInfo, setPageInfo] = useState<any>({
    pageName: "智能合同审查助手",
    pageDesc: "法审、敏感词、批注",
    pageInputDesc: "用户上传需要进行法审的合同内容",
    pageOutputDesc: "含有批注内容的word文档",
    steps: [
      {
        title: "文档拆分",
        content: "<SplitviewModule />",
        submitName: "",
        isExport: false,
        agentId: "",
      },
      {
        title: "实体提取",
        content: "<EntityExtractionModule />",
        submitName: "",
        isExport: false,
        agentId: "66c19948-5427-4c0d-b25a-5eb87ebfd989",
        inputDesc: "用户上传需要被提取的信息内容清单和合同内容片段",
        outputDesc: "从合同内容片段中提取到实体信息按照要求的格式进行输出",
        inputSource: "文档切分，场景规划师",
      },
      {
        title: "规则与目标确认",
        content: "<TargetingModule />",
        submitName: "",
        isExport: false,
        agentId: "a5f3cb88-d63b-417c-ab81-90c1307e2c31",
        inputDesc: "用户上传需要被提取的信息内容清单和合同内容片段",
        outputDesc: "从合同内容片段中提取到实体信息按照要求的格式进行输出",
      },
      {
        title: "合同法审",
        content: "<ContractLawReview />",
        submitName: "",
        isExport: true,
        agentId: "06d5b6f0-fdd1-4f7c-91d7-4f73c5f89ad1",
        inputDesc: "用户上传合同内容和规则条例",
        outputDesc: "输出合同校验结果",
        inputSource: "规则匹配",
      },
    ],
  });

  const [isQuentially, setIsQuentially] = useState(false);
  const splitTypeOptions = [{ label: "按章节切分", value: "按章节切分" }];
  const [current, setCurrent] = useState(-1);
  // 2️⃣ 用 ref 始终保存最新的 current 值（避免闭包问题）
  const currentRef = useRef(current);
  const [globalLoading, setGlobalLoading] = useState(false); // 全局加载状态
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]); // 上传的文件信息
  const [originalFile, setOriginalFile] = useState<any>(null); // 原始上传的文件
  const [splitType, setSplitType] = useState(splitTypeOptions[0].value); // 切分类型
  const [sceneReviewShow, setSceneReviewShow] = useState(false); // 是否要展示场景评审
  const [sceneReviewContent, setSceneReviewContent] = useState<any>(null); // 输入输出评审的内容
  const [reviewResult, setReviewResult] = useState<any>(null); // 输入输出评审的结果
  const [scenarioSteps, setScenarioSteps] = useState<any[]>([]); // 场景评审步骤
  const isOutputReview = useRef(false); // 看是输入评审还是输出评审
  const [stepData, setStepData] = useState<any>({}); // 所有步骤的数据. outputReview 输出评审是否通过  inputReview 输入评审是否通过

  // 所有组件的 ref 映射
  const refs = useRef<Record<number, RefObject<any>>>({});

  //取 pageInfo 自己的基本信息 + steps 里某一步
  const getStepData = (pageInfo: any, stepIndex: number) => {
    const { steps, ...pageBase } = pageInfo; // 把 steps 拆出来，剩下的是 pageInfo 自身信息
    const step = steps[stepIndex]; // 拿到某一步

    return {
      ...pageBase,
      step, // 保留单步
    };
  };
  useEffect(() => {
    currentRef.current = current;
  }, [current]);

  // 已加载的组件缓存
  const [loadedComponents, setLoadedComponents] = useState<{
    [key: number]: React.ComponentType<any>;
  }>({});

  // 按需加载组件
  const loadComponent = useCallback(
    (stepIndex: number) => {
      if (loadedComponents[stepIndex]) {
        return loadedComponents[stepIndex];
      }

      const step = pageInfo.steps[stepIndex];
      const match = step?.content.match(/<(\w+)\s*\/>/);
      const componentName = match?.[1];

      if (componentName) {
        const modulePath = `/src/businessComponents/${componentName}/index.tsx`;
        const loader = modules[modulePath];

        if (loader) {
          // 创建 ref（如果不存在）
          if (!refs.current[stepIndex]) {
            refs.current[stepIndex] = createRef();
          }

          const LazyComponent = React.lazy(loader as any);
          const ComponentWrapper = (props: any) => (
            <LazyComponent ref={refs.current[stepIndex]} {...props} />
          );

          // 缓存已加载的组件
          setLoadedComponents((prev) => ({
            ...prev,
            [stepIndex]: ComponentWrapper,
          }));

          return ComponentWrapper;
        } else {
          console.warn("模块未找到:", modulePath);
        }
      }

      return null;
    },
    [pageInfo.steps, loadedComponents]
  );

  // 提交操作
  const getSubmitInfo = async () => {
    const ref = refs.current[currentRef.current];
    let childData = null;
    if (currentRef.current == 0) {
      childData = await ref?.current?.triggerSplit?.();
    } else if (currentRef.current == 1) {
      const data = await ref?.current?.triggerSplit?.();
      childData = await ref?.current?.triggerSplit?.();
      if (data.isEntity == true) {
        await ref?.current?.showModal?.();
        return;
      }
    } else if (currentRef.current == 2) {
      childData = await ref?.current?.triggerSplit?.();
    } else {
      childData = await ref?.current?.getMentionsData?.();
    }
    console.log(childData);
    // 存到父组件 state
    setStepData((prev: any) => ({
      ...prev,
      [currentRef.current]: childData,
    }));
    if (currentRef.current < pageInfo.steps.length - 1) {
      setCurrent(current + 1);
      // const obj = getStepData(pageInfo, current + 1);
      // if (!stepData[current + 1]?.inputReview) {
      //   getUnitReviewInfo(
      //     false,
      //     VITE_UNIT_INPUT,
      //     "1",
      //     obj,
      //   );
      // }
    }
  };
  // 进入下一步 由子组件跳转进入
  const handleIncrement = async () => {
    const ref = refs.current[currentRef.current];
    const childData = await ref?.current?.triggerSplit?.();
    setStepData((prev: any) => ({
      ...prev,
      [currentRef.current]: childData,
    }));
    if (currentRef.current < pageInfo.steps.length - 1) {
      setCurrent(current + 1);
    }
  };

  // 上传文件
  const beforeUpload = async (file: File) => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf("."));
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    setGlobalLoading?.(true);
    if (["docx", "doc"].includes(originalFileExt)) {
      convertFileToPDF(file).then(async (response) => {
        if (response["status"] && response["status"] !== 200) {
          setGlobalLoading?.(false);
          message.open({
            key: "uploading",
            type: "error",
            content: "文件处理异常，请稍后重试",
            duration: 1,
          });
        } else if ("blob" in response) {
          const userInfo = await getUserInfo();
          const blob = await response.blob();
          const pdfFile = new File([blob], `${originalFilename}.pdf`, {
            type: "application/pdf",
          });
          const fileData = {
            fileName: file.name,
            fileStr: await fileToBase64(file),
            path: "/files/upload",
            agentId: "66c19948-5427-4c0d-b25a-5eb87ebfd989",
            user: userInfo?.id,
            libName: file.name,
            libDesc: "",
            flag: "file",
          };
          uploadChatFile(fileData).then(async (response: any) => {
            setGlobalLoading?.(false);
            if (response.code == 200) {
              setUploadedFiles([
                { url: URL.createObjectURL(pdfFile), ...response.data },
              ]);
              setOriginalFile(file);
              message.open({
                key: "uploading",
                type: "success",
                content: "文件上传成功",
                duration: 1,
              });
            } else {
              message.open({
                key: "uploading",
                type: "error",
                content: "文件上传失败",
                duration: 1,
              });
            }
          });
        }
      });
    }
  };
  // 首页点击下一步
  const nextContent = async () => {
    if (uploadedFiles.length === 0) {
      message.error("请先上传文件");
      return;
    }
    setStepData((prev: any) => ({
      ...prev,
      startData: {
        ...prev.startData,
        originalFile: originalFile, // 原始文件
        uploadedFiles: uploadedFiles, // pdf的文件
        splitType: splitType, // 拆分方式
      },
    }));
    setCurrent(0);
    const obj = getStepData(pageInfo, 0);
    getUnitReviewInfo(
      false,
      VITE_UNIT_INPUT,
      "1",
      obj,
      stepData?.startData?.uploadedFiles
    );
    // if (stepData?.startData?.inputReview) {
    //   // 首页已经规划
    //   // 说明是已经规划过
    //   setCurrent(0);
    //   if (!stepData[0]?.inputReview) {
    //     // 说明是已经输入还没有评审过
    //     const obj = getStepData(pageInfo, 0);
    //     getUnitReviewInfo(
    //       false,
    //       VITE_UNIT_INPUT,
    //       "1",
    //       obj,
    //       stepData?.startData?.uploadedFiles
    //     );
    //   }
    // } else {
    //   setSceneReviewShow(true);
    //   getReviewInfo(
    //     VITE_SCENE_INPUT,
    //     `选择拆分方式:按章节拆分`,
    //     pageInfo,
    //     uploadedFiles
    //   );
    // }
  };
  // 开始场景规划轮训处理
  const sceneReviewStartFn = async () => {
    setGlobalLoading(true);
    const result: any[] = [];
    // 循环调用每个步骤，一个完成后再调用下一个
    for (const item of pageInfo.steps) {
      const stepResult: any = await getPlanning({
        pageName: pageInfo.pageName,
        pageDesc: pageInfo.pageDesc,
        pageInputDesc: pageInfo.pageInputDesc,
        pageOutputDesc: pageInfo.pageOutputDesc,
        steps: [item],
      });
      const jsonStr = extractJsonString(stepResult);
      // try {
      if (typeof jsonStr == "string") {
        // 是 JSON 字符串，需要解析
        result.push(JSON.parse(jsonStr));
      } else if (typeof jsonStr == "object") {
        // 是 JSON 对象，可以直接使用
        result.push(jsonStr);
      } else {
        console.error("解析 JSON 失败: ", jsonStr);
      }
    }
    // 1) 融合：将规划结果与原始 step 元数据合并（按顺序一一对应）
    const merged = pageInfo.steps.map((step: any, idx: any) => ({
      ...step,
      ...(result[idx] || {}),
    }));
    setScenarioSteps(merged);
    setGlobalLoading(false);
  };
  // 规划
  const getPlanning = async (data: any) => {
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    const tenantId = cacheGet("tenantId");
    return new Promise((resolve) => {
      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: VITE_SCENE_PLANNING || "",
          agentId: VITE_SCENE_PLANNING || "",
          path: "/chat-messages",
          query: "1",
          difyJson: {
            inputs: {
              Token: tokenInfo || "",
              tenantid: tenantId || "",
              pageInfo: JSON.stringify(data),
            },
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: "1",
          },
        },
        query: {},
        message: "1",
        onFinished: (result: any) => {
          resolve(result); // 将结果传出去
        },
      });
    });
  };

  // 场景输入输出评审信息
  const getReviewInfo = async (
    agentId: string,
    inputQuery: string,
    pageInfo: any,
    uploadedFileData: any
  ) => {
    const fileData: any[] = [];
    setGlobalLoading(true);
    uploadedFileData.forEach((item: any) => {
      fileData.push({
        type: item.fileType || "document",
        transfer_method: "local_file",
        upload_file_id: item.id,
      });
    });
    try {
      if (agentId) {
        const tokenInfo = await getToken();
        const userInfo = await getUserInfo();
        const tenantId = cacheGet("tenantId");
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },

          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId,
            agentId: agentId,
            path: "/chat-messages",
            query: inputQuery || "",
            difyJson: {
              inputs: {
                Token: tokenInfo || "",
                tenantid: tenantId || "",
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: inputQuery || "",
              files: fileData,
              // pageInfo: JSON.stringify(pageInfo),
            },
          },
          query: {},
          message: inputQuery || "",
          onMessage: (res: any) => {
            console.log(res, "输入/输出评审内容");
          },
          onFinished: (res) => {
            console.log(res, "输入/输出评审完成");
            if (res) {
              const jsonStr = extractJsonString(res);
              try {
                const parsed = JSON.parse(jsonStr);
                setSceneReviewContent(parsed);
                if (parsed["评审结果"] == "正常") {
                  setReviewResult(parsed["评审结果"] == "正常");
                  // 开始场景规划
                  sceneReviewStartFn();
                } else {
                  setGlobalLoading(false);
                }
              } catch (e) {
                console.error("输入/输出评审结果解析失败:", e, jsonStr);
              }
            }
          },
        });
      }
    } catch (error) {
      setGlobalLoading(false);
    }
  };

  // 忽略错误，继续下一步
  const continueScenario = () => {
    // 这个是场景评审的
    sceneReviewStartFn();
  };

  // 规划页面修改 确认无误
  const confirmScenario = (steps: any) => {
    setPageInfo({
      ...pageInfo,
      steps: steps,
    });
    setSceneReviewShow(false);
    setStepData((prev: any) => ({
      ...prev,
      startData: {
        ...prev.startData,
        inputReview: true,
      },
    }));
    console.log(currentRef.current, 234);
    if (currentRef.current < 0) {
      setCurrent(0);
      console.log(stepData?.startData?.inputReview, 21331);
      if (!stepData?.startData?.inputReview) {
        // 说明是已经输入还没有评审过
        const obj = getStepData(pageInfo, 0);
        getUnitReviewInfo(
          false,
          VITE_UNIT_INPUT,
          "1",
          obj,
          stepData?.startData?.uploadedFiles
        );
      }
    }
  };

  // 单元场景的评审～～～～～～～～～～～～～～～～～～～～～～～～～～～～～～～

  // 输入评审完毕后，调用各个单元的提交.  重新生成
  const getSubmit = () => {
    const ref = refs.current[currentRef.current];
    console.log(currentRef.current, "第几步了");
    if (currentRef.current == 0) {
      ref?.current?.splitDataFiles?.();
    } else if (currentRef.current == 1) {
      ref?.current?.getQualityData?.();
    } else if (currentRef.current == 2) {
      ref?.current?.getRuleData?.();
    } else if (currentRef.current == 3) {
      ref?.current?.getContractData?.();
    }
  };

  // 点击确定
  // const onConfirm = () => {
  //   if (isOutput) {
  //     console.log(stepData);
  //     console.log(32131);
  //     // 说明是输出评审
  //     // setStepData((prev) => ({
  //     //   ...prev,
  //     //   [current]: {
  //     //     ...prev[current],
  //     //     outputReview: false, // 修改其中某个 key
  //     //   },
  //     // }));
  //   } else {
  //     // 说明是输入评审
  //     setStepData((prev) => ({
  //       ...prev,
  //       [current]: {
  //         ...prev[current],
  //         inputReview: true, // 修改其中某个 key
  //       },
  //     }));
  //     // 说明是输入评审
  //     getSubmit();
  //   }
  // };
  // 单元场景评审失败， 继续下一步点击
  const unitContinueScenario = () => {
    console.log(isOutputReview.current);
    // 看是输入场景 还是输出场景
    if (isOutputReview.current) {
      // 说明是输出评审
      setStepData((prev: any) => ({
        ...prev,
        [currentRef.current]: {
          ...prev[currentRef.current],
          outputReview: true, // 修改其中某个 key
        },
      }));
    } else {
      // 说明是输入评审
      setStepData((prev: any) => ({
        ...prev,
        [currentRef.current]: {
          ...prev[currentRef.current],
          inputReview: true, // 修改其中某个 key
        },
      }));
      // 说明是输入评审
      getSubmit();
    }
  };
  // 子组件调用输出评审（子组件传过来的）
  // stepCurrent 第几步
  // stepCurrentData 第几步的数据
  const handleFromChild = (type: string, stepCurrentData: any) => {
    const obj = getStepData(pageInfo, currentRef.current);
    let isRtye = false;
    if (type == "输出") {
      isRtye = true;
    }
    getUnitReviewInfo(isRtye, VITE_UNIT_OUTPUT, stepCurrentData, obj);
  };
  // 输入输出评审单元的
  const getUnitReviewInfo = async (
    isOutput: boolean,
    agentId: string,
    inputQuery?: string,
    pageInfo?: any,
    uploadedFileData?: any
  ) => {
    const fileData: any[] = [];
    setGlobalLoading(true);
    setSceneReviewContent(null);
    if (uploadedFileData && uploadedFileData.length > 0) {
      uploadedFileData?.forEach((item: any) => {
        fileData.push({
          type: item.fileType || "document",
          transfer_method: "local_file",
          upload_file_id: item.id,
        });
      });
    }
    isOutputReview.current = isOutput; // 看当前是输入还是输出评审
    try {
      if (agentId) {
        const tokenInfo = await getToken();
        const userInfo = await getUserInfo();
        const tenantId = cacheGet("tenantId");
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },

          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId,
            agentId: agentId,
            path: "/chat-messages",
            query: inputQuery || "",
            difyJson: {
              inputs: {
                Token: tokenInfo || "",
                tenantid: tenantId || "",
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: inputQuery || "",
              files: fileData,
              // pageInfo: JSON.stringify(pageInfo),
            },
          },
          query: {},
          message: inputQuery || "",
          onMessage: (res: any) => {
            console.log(res, "输入/输出评审内容");
          },
          onFinished: (res) => {
            if (res) {
              const jsonStr = extractJsonString(res);
              try {
                const parsed = JSON.parse(jsonStr);
                setSceneReviewContent(parsed);
                console.log("评审结果");
                console.log("评审结果", currentRef.current, "结果");
                if (parsed["评审结果"] == "正常") {
                  setGlobalLoading(false); // 评审结果正常
                  setReviewResult(parsed["评审结果"] == "正常");
                  if (isOutput) {
                    console.log(stepData);
                    console.log(32131);
                    // 说明是输出评审
                    // setStepData((prev) => ({
                    //   ...prev,
                    //   [current]: {
                    //     ...prev[current],
                    //     outputReview: false, // 修改其中某个 key
                    //   },
                    // }));
                  } else {
                    console.log(currentRef.current, 999999999);
                    // 说明是输入评审
                    // setStepData((prev) => ({
                    //   ...prev,
                    //   [currentRef.current]: {
                    //     ...prev[currentRef.current],
                    //     inputReview: true, // 修改其中某个 key
                    //   },
                    // }));
                    // 说明是输入评审
                    // getSubmit();
                  }
                } else {
                  setGlobalLoading(false);
                }
              } catch (e) {
                console.error("输入/输出评审结果解析失败:", e, jsonStr);
              }
            }
          },
        });
      }
    } catch (error) {
      setGlobalLoading(false);
    }
  };

  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Flex vertical className="legal-review-page">
        {/* 首页 */}
        {current < 0 ? (
          <Flex vertical className="legal-review-home">
            <HeaderCom
              mainTitle={pageInfo.pageName}
              subTitle={pageInfo.pageDesc}
            />
            {sceneReviewShow ? (
              <ReviewResult
                type="scene"
                steps={scenarioSteps.length ? scenarioSteps : pageInfo.steps}
                showScenario={scenarioSteps.length > 0}
                reviewContent={sceneReviewContent}
                reviewResult={reviewResult}
                onBackIndex={() => {
                  setSceneReviewShow(false);
                }}
                onContinue={() => continueScenario()}
                onConfirmScenario={(steps) => confirmScenario(steps)}
              />
            ) : (
              <Flex
                vertical
                gap="middle"
                style={{ flex: 1 }}
                className="splitting-form"
              >
                <Form layout="vertical">
                  <Form.Item>
                    <Flex className="upload">
                      <Flex vertical gap="4" style={{ flex: 1 }}>
                        <Typography.Title className="file-title" level={5}>
                          合同上传
                        </Typography.Title>
                        <Upload.Dragger
                          showUploadList={false}
                          multiple={false}
                          beforeUpload={beforeUpload}
                          accept=".docx"
                          fileList={uploadedFiles}
                        >
                          <img
                            src={uploadIcon}
                            style={{ width: 45, margin: "0px auto" }}
                          />
                          <p className="ant-upload-hint">
                            {uploadedFiles && uploadedFiles.length > 0 ? (
                              <span>{uploadedFiles[0].name}</span>
                            ) : (
                              <span>点击或将文件拖到此处上传</span>
                            )}
                            <span>支持word格式文档</span>
                          </p>
                        </Upload.Dragger>
                      </Flex>
                    </Flex>
                  </Form.Item>
                  <Form.Item
                    label={
                      <>
                        <span style={{ color: "#000", fontWeight: "bold" }}>
                          选择拆分方式
                        </span>
                      </>
                    }
                  >
                    <Select
                      value={splitType}
                      options={splitTypeOptions}
                      onChange={setSplitType}
                      placeholder="请选择拆分方式"
                    />
                  </Form.Item>
                </Form>
                <Button
                  size="large"
                  type="primary"
                  className="upload-btn"
                  onClick={nextContent}
                >
                  下一步
                </Button>
              </Flex>
            )}
          </Flex>
        ) : (
          // 步骤页
          <Flex vertical className="legal-review-steps">
            <Flex className="info-con-steps" justify="center" align="center">
              <div className="content-title">
                <p>智能合同审查助手</p>
                <p>法审、敏感词、批注</p>
              </div>
              <Steps current={currentRef.current} className="steps-con">
                {pageInfo.steps.map((item: any) => (
                  <Steps.Step key={item.title} title={item.title} />
                ))}
              </Steps>
            </Flex>
            <Flex vertical className="legal-page-con" justify="center">
              <div
                style={{
                  height: "calc(100vh - 148px)",
                }}
              >
                <Suspense fallback={<div>加载中...</div>}>
                  {/* 评审结果显示 */}
                  <div
                    style={{
                      display:
                        !stepData[currentRef.current]?.inputReview ||
                        (!stepData[currentRef.current]?.outputReview &&
                          isOutputReview.current)
                          ? "block"
                          : "none",
                    }}
                  >
                    <ReviewResult
                      type="unit"
                      steps={
                        scenarioSteps.length ? scenarioSteps : pageInfo.steps
                      }
                      title={
                        isOutputReview.current ? "场景输出评审" : "场景输入评审"
                      }
                      showScenario={scenarioSteps.length > 0}
                      reviewContent={sceneReviewContent}
                      reviewResult={reviewResult}
                      onBackIndex={() => {
                        setSceneReviewShow(false);
                      }}
                      onContinue={() => unitContinueScenario()}
                    />
                  </div>

                  {/* 按需渲染组件 */}
                  {pageInfo.steps.map((step: any, stepIndex: number) => {
                    const isCurrentStep = stepIndex === currentRef.current;
                    const shouldShowComponent =
                      stepData[stepIndex]?.inputReview &&
                      (stepData[stepIndex]?.outputReview ||
                        !isOutputReview.current);

                    // 只有当前步骤且应该显示组件时才加载组件
                    if (!isCurrentStep || !shouldShowComponent) {
                      return null;
                    }

                    // 按需加载组件
                    const Component = loadComponent(stepIndex);

                    if (!Component) {
                      return (
                        <div key={stepIndex}>组件未找到: {step.content}</div>
                      );
                    }

                    return (
                      <div key={stepIndex}>
                        <Component
                          setGlobalLoading={setGlobalLoading}
                          agentId={step.agentId}
                          splitViewData={{
                            originalFile: stepData?.startData?.originalFile,
                            fileList: stepData?.startData?.uploadedFiles,
                          }} // 第一步页面用的数据
                          entityViewData={{
                            fileList: stepData?.startData?.uploadedFiles,
                            chunks: stepData[0]?.chunksData,
                          }} // 第二步页面用的数据
                          currentEchoData={stepData[stepIndex]} // 父组件当前的值用于回显跟判断 有无输入输出
                          targetingData={{
                            // 第三步页面用的数据
                            fileList: stepData?.startData?.uploadedFiles,
                            ruleFrom: stepData[1]?.ruleFormData,
                            chunks: stepData[0]?.chunksData, // 拆分后的数据
                          }}
                          onIncrement={handleIncrement} // 子组件调用父组件的方法进入下一步
                          onCallParent={handleFromChild} // 子组件调用父组件的方法 进行单元场景输出处理
                          lawReviewData={{
                            // 最后一步页面用的数据
                            targentData: stepData[2]?.targentData, // 目标的数据
                            rulesData: stepData[2]?.rulesData,
                            ruleFrom: stepData[1]?.ruleFormData,
                            fileList: stepData?.startData?.uploadedFiles,
                          }}
                          pageInfo={{
                            pageName: pageInfo.pageName,
                            pageDesc: pageInfo.pageDesc,
                            pageInputDesc: pageInfo.pageInputDesc,
                            pageOutputDesc: pageInfo.pageOutputDesc,
                            steps: [step],
                          }}
                        />
                      </div>
                    );
                  })}
                </Suspense>
              </div>

              <Flex
                justify="center"
                align="center"
                gap={token.marginMD}
                className="legal-review-footer"
              >
                <Button
                  style={{ minWidth: "100px", marginRight: 8 }}
                  onClick={() => {
                    setCurrent(-1);
                  }}
                >
                  返回首页
                </Button>

                {current > 0 && (
                  <Button
                    style={{ minWidth: "100px", marginRight: 8 }}
                    onClick={() => {
                      setCurrent(current - 1);
                    }}
                  >
                    上一步
                  </Button>
                )}
                <Button
                  icon={<RedoOutlined />}
                  onClick={() => {
                    getSubmit();
                  }}
                >
                  重新生成
                </Button>
                <Button icon={<IconFont type="knowledgeBaseOutlined" />}>
                  保存知识库
                </Button>
                {current < pageInfo.steps.length - 1 && (
                  <Button
                    type="primary"
                    onClick={getSubmitInfo}
                    style={{ minWidth: "100px", marginRight: 8 }}
                  >
                    {pageInfo.steps[current]?.submitName || "下一步"}
                  </Button>
                )}
                {pageInfo.steps[current]?.isExport && (
                  <Button
                    type="primary"
                    onClick={() => console.log("导出操作")}
                  >
                    导出
                  </Button>
                )}
              </Flex>
            </Flex>
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default DraftPage;
