.interview-page {
  width: 100%;
  height: 100vh;
  padding-left: 0;
  transition: padding-left 0.3s ease;
}
.interview-page .main-content {
  transition: margin-left 0.3s ease;
}
.interview-page .main-content.sidebar-open {
  margin-left: 300px;
}
.interview-page .interview-page-con {
  margin-top: 20px;
  flex: 1;
}
.interview-page .custome-steps {
  padding: 0 10vw 24px;
}
.interview-page .dynamic-component {
  min-height: 300px;
  margin-bottom: var(--ant-margin-lg);
  width: 80vw;
  height: calc(-200vh);
}
.interview-page .start-btn {
  margin-top: var(--ant-margin-xxl);
}
