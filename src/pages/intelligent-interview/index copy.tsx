// DraftPage.tsx
import React, {
  useState,
  useMemo,
  Suspense,
  useRef,
  createRef,
  RefObject,
  useEffect,
} from 'react'
import { Button, Flex, Steps, Card, Tag, Divider, message, Spin } from 'antd'
import { getToken, getUserInfo } from '@/utils/auth'
import { cacheGet } from '@/utils/cacheUtil'
import useSSEChat from '@/hooks/useSSEChat'
import { useGetState } from "ahooks";

import HeaderCom from '@/component/Header'
import MentionsComponent from '@/businessComponents/MentionsFileModule'
import LeftSidebarModule from '@/businessComponents/LeftSidebarModule'
import type { MentionsComponentRef } from '@/businessComponents/MentionsFileModule'
import type { TabItem } from '@/businessComponents/TabContainer'
import type { TabsProps } from 'antd'

import './index.less'
import { queryObjects } from 'v8'
import { userInfo } from 'os'

// 输入输出评审agentId
const reviewAgentId = {
  场景输入官: '5aa01041-7e2b-4221-ab8d-a11d8db3ca8e',
  场景输出官: 'a40e5a31-da0a-4b70-91c3-1201baebfc9b',
  单元输入官: '1f72ff5f-d6b8-48e7-90ad-294cf58ab136',
  单元输出官: 'd00b1144-ad49-4c60-ac49-1cd16cf4bfeb',
  场景规划师: '871bc0ed-4d86-4b87-aa3c-2bc187e5da25',
}

const DraftPage: React.FC = () => {
  const sseChat = useSSEChat()
  const sseChat2 = useSSEChat()
  const [current, setCurrent] = useState(0)
  const [started, setStarted] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState('1')
  const [sidebarVisible, setSidebarVisible] = useState(false)
  const [uploadedFileData, setUploadedFileData] = useState<any>(null) // 新增：保存上传的文件数据
  // 步骤数据状态，包括各步骤的结果和开始数据
  const [stepData, setStepData] = useState<{
    [key: number]: any // 各步骤的数据
    startData?: {
      // 开始流程时的数据
      query: string
      localFile: any[]
      timestamp: number
    }
    agentId?: string // agentId
  }>({})
  const [stepData2, setStepData2] = useState<any>({})

  // 全局加载状态管理
  const [globalLoading, setGlobalLoading] = useState(false)
  const [reviewLoading, setReviewLoading] = useState(false)

  //场景输入开始
  const [sceneReviewStart, setSceneReviewStart] = useState(false)

  // // 将pageInfo移到组件内部，使其能够访问uploadedFileData状态
  // const pageInfo = {
  //   pageName: "智能面试助手",
  //   pageDesc: "上传简历，相关信息，简历解析，智能出题，在线面试，面试总结",
  //   steps: [
  //     {
  //       title: "简历解析",
  //       content: "<InterviewModule />",
  //       submitName: "提交信息",
  //       leftTabs: [
  //         {
  //           title: "简历预览",
  //           content: "ResumePreview",
  //           key: "resume-preview",
  //           props: {
  //             stepIndex: 0,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //       ],
  //       isExport: false,
  //       agentId: "ea543945-0d56-42a3-98bb-f06a3f4dd2f6",
  //     },
  //     {
  //       title: "智能出题",
  //       content: "<IntelligentQuestionModule />",
  //       submitName: "选择模版1",
  //       leftTabs: [
  //         {
  //           title: "简历预览",
  //           content: "ResumePreview",
  //           key: "resume-preview-2",
  //           props: {
  //             stepIndex: 1,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //         {
  //           title: "简历解析",
  //           content: "ResumeAnalysis",
  //           key: "resume-analysis-2",
  //           props: {
  //             stepIndex: 1,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //         {
  //           title: '岗位匹配',
  //           content: "JobMatching",
  //           key: "job-matching-2",
  //           props: {
  //             stepIndex: 1,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         }
  //       ],
  //       isExport: false,
  //       agentId: "54331e66-df8b-48f3-b918-736b57f59d64",
  //     },
  //     {
  //       title: "安排面试",
  //       content: "<InterviewArrangementModule />",
  //       submitName: "生成大纲1",
  //       isExport: false,
  //       agentId: "",
  //     },
  //     {
  //       title: "面试会议",
  //       content: "<InterviewModule />",
  //       leftTabs: [
  //         {
  //           title: "简历预览",
  //           content: "ResumePreview",
  //           key: "resume-preview-4",
  //           props: {
  //             stepIndex: 3,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //         {
  //           title: "简历解析",
  //           content: "ResumeAnalysis",
  //           key: "resume-analysis-4",
  //           props: {
  //             stepIndex: 3,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //         {
  //           title: '岗位匹配',
  //           content: "JobMatching",
  //           key: "job-matching-4",
  //           props: {
  //             stepIndex: 3,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //         {
  //           title: '智能题目',
  //           content: "QuestionGeneration",
  //           key: "question-generation-4",
  //           props: {
  //             stepIndex: 3,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         }
  //       ],
  //       submitName: "生成合同",
  //       isExport: true,
  //       agentId: "9c8f1695-4gee-673g-d685-2f8adcg92ch",
  //     },
  //     {
  //       title: "评估报告",
  //       content: "<InterviewModule />",
  //       submitName: "生成合同",
  //       leftTabs: [
  //         {
  //           title: "简历预览",
  //           content: "ResumePreview",
  //           key: "resume-preview-5",
  //           props: {
  //             stepIndex: 4,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //         {
  //           title: "简历解析",
  //           content: "ResumeAnalysis",
  //           key: "resume-analysis-5",
  //           props: {
  //             stepIndex: 4,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //         {
  //           title: '岗位匹配',
  //           content: "JobMatching",
  //           key: "job-matching-5",
  //           props: {
  //             stepIndex: 4,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         },
  //         {
  //           title: '智能题目',
  //           content: "QuestionGeneration",
  //           key: "question-generation-5",
  //           props: {
  //             stepIndex: 4,
  //             fileData: uploadedFileData // 传递文件数据
  //           }
  //         }
  //       ],
  //       isExport: true,
  //       agentId: "7bdd055f-bcc9-4f89-ba23-a2668b1196f2",
  //     },
  //   ],
  // };

  // 将pageInfo移到组件内部，使其能够访问uploadedFileData状态
  const pageInfo = {
    pageName: '智能面试助手',
    pageDesc: '上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
    pageInputDesc:
      '请上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
    pageOutputDesc:
      '请上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
    steps: [
      {
        title: '简历解析',
        content: '<ResumeAnalysisModule />',
        agentId: 'ea543945-0d56-42a3-98bb-f06a3f4dd2f6',
      },
      {
        title: '智能出题',
        content: '<IntelligentQuestionModule />',
        agentId: '54331e66-df8b-48f3-b918-736b57f59d64',
      },
      {
        title: '面试安排',
        content: '<InterviewArrangementModule />',
        agentId: '',
      },
      {
        title: '视频面试',
        content: '<VideoInterviewModule />',
        agentId: '9c8f1695-4gee-673g-d685-2f8adcg92ch',
      },
      {
        title: '面试转录',
        content: '<TranscriptModule />',
        agentId: '9c8f1695-4gee-673g-d685-2f8adcg92ch',
      },
      {
        title: '评估报告',
        content: '<EvaluationReportModule />',
        agentId: '7bdd055f-bcc9-4f89-ba23-a2668b1196f2',
      },
    ],
  }

  const currentStep = pageInfo.steps[current]
  const mentionsRef = useRef<MentionsComponentRef>(null)

  // 自动匹配业务组件
  const modules = import.meta.glob('/src/businessComponents/**/index.tsx')

  // 所有组件的 ref 映射
  const refs = useRef<Record<number, RefObject<any>>>({})

  // 懒加载当前组件
  const DynamicComponent = useMemo(() => {
    if (!started) return null
    const match = currentStep.content.match(/<(\w+)\s*\/>/)
    const componentName = match?.[1]
    console.log('当前组件名称:', componentName)

    if (!componentName) return null

    const modulePath = `/src/businessComponents/${componentName}/index.tsx`
    const loader = modules[modulePath]

    if (!loader) {
      console.warn('模块未找到:', modulePath)
      return null
    }

    // 创建 ref（如果不存在）
    if (!refs.current[current]) {
      refs.current[current] = createRef()
    }

    const LazyComponent = React.lazy(loader as any)

    // 返回一个函数，该函数接收props并渲染组件
    return (props: any) => (
      <LazyComponent ref={refs.current[current]} {...props} />
    )
  }, [current, started])

  // 开始流程操作 - 获取开始数据并启动面试流程
  const getStartInfo = async () => {
    let childData: any

    // 如果还没有开始，从mentionsRef获取数据
    if (!started) {
      // 获取用户输入的文件和查询内容
      childData = mentionsRef.current?.getMentionsData?.()
      console.log('开始按钮点击，获取MentionsComponent数据:', childData)

      // 验证必要数据：必须有查询内容和上传的文件
      if (!childData?.query || !childData?.localFile?.length) {
        message.warning('请先输入内容并上传文件')
        return
      }

      try {
        // 保存文件数据到状态中，供后续步骤使用
        setUploadedFileData(childData.localFile)
        // 保存开始数据到步骤状态中，供子组件使用
        setStepData((prev) => ({
          ...prev,
          startData: {
            query: childData.query,
            localFile: childData.localFile,
            timestamp: Date.now(),
          },
        }))

        // // 开始流程：显示步骤条，进入第一步
        // setStarted(true);
        // setCurrent(0);
        // return
        

        getReviewInfo(
          reviewAgentId['场景输入官'],
          childData.query,
          pageInfo,
          childData.localFile
        )
        // sceneReviewStartFn()
      } catch (error) {
        console.error('启动面试流程失败:', error)
        // setGlobalLoading(false);
        message.error('启动面试流程失败，请重试')
      }
    }
  }
  // 获取输入/输出评审信息
  const getReviewInfo = async (
    agentId: string,
    inputQuery: string,
    pageInfo: any,
    uploadedFileData: any
  ) => {
    console.log(stepData, 'stepData')
    const fileData: any[] = []
    setReviewLoading(true)
    uploadedFileData.forEach((item: any) => {
      fileData.push({
        type: item.fileType || 'document',
        transfer_method: 'local_file',
        upload_file_id: item.id,
      })
    })
    try {
      if (agentId) {
        const tokenInfo = await getToken()
        const userInfo = await getUserInfo()
        const tenantId = cacheGet('tenantId')
        sseChat.start({
          url: '/dify/broker/agent/stream',
          headers: {
            'Content-Type': 'application/json',
            Token: tokenInfo || '',
          },

          body: {
            insId: '1',
            bizType: 'app:agent',
            bizId: agentId,
            agentId: agentId,
            path: '/chat-messages',
            query: inputQuery || '',
            difyJson: {
              inputs: {
                Token: tokenInfo || '',
                tenantid: tenantId || '',
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: 'streaming',
              user: userInfo?.id || 'anonymous',
              conversation_id: '',
              query: inputQuery || '',
              files: fileData,
              // pageInfo: JSON.stringify(pageInfo),
            },
          },
          query: {},
          message: inputQuery || '',
          onMessage: (res: any) => {
            console.log(res, '输入/输出评审内容')
          },
          onFinished: (res) => {
            console.log(res, '输入/输出评审完成')
            setReviewLoading(false)
            // 开始场景输入
            sceneReviewStartFn()
            const pageInfoData = {
              pageName: pageInfo.pageName,
              pageDesc: pageInfo.pageDesc,
              pageInputDesc: pageInfo.pageInputDesc,
              pageOutputDesc: pageInfo.pageOutputDesc,
              steps: pageInfo.steps[0]
            }
            // getReviewInfo2(
            //   reviewAgentId['场景规划师'],
            //   inputQuery,
            //   pageInfoData,
            //   uploadedFileData
            // )
            // if (current < pageInfo.steps.length - 1) {
            //   setCurrent(current + 1);
            // }
          },
        })
      }
    } catch (error) {
      setReviewLoading(false)
    }
  }

  // 获取输入/输出评审信息
  const getReviewInfo2 = async (
    agentId: string,
    inputQuery: string,
    pageInfo: any,
    uploadedFileData: any
  ) => {
    console.log(stepData, 'stepData')
    const fileData: any[] = []
    setReviewLoading(true)
    uploadedFileData.forEach((item: any) => {
      fileData.push({
        type: item.fileType || 'document',
        transfer_method: 'local_file',
        upload_file_id: item.id,
      })
    })
    try {
      if (agentId) {
        const tokenInfo = await getToken()
        const userInfo = await getUserInfo()
        const tenantId = cacheGet('tenantId')
        sseChat.start({
          url: '/dify/broker/agent/stream',
          headers: {
            'Content-Type': 'application/json',
            Token: tokenInfo || '',
          },

          body: {
            insId: '1',
            bizType: 'app:agent',
            bizId: agentId,
            agentId: agentId,
            path: '/chat-messages',
            query: inputQuery || '',
            difyJson: {
              inputs: {
                Token: tokenInfo || '',
                tenantid: tenantId || '',
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: 'streaming',
              user: userInfo?.id || 'anonymous',
              conversation_id: '',
              query: inputQuery || '',
              files: fileData,
              // pageInfo: JSON.stringify(pageInfo),
            },
          },
          query: {},
          message: inputQuery || '',
          onMessage: (res: any) => {
            console.log(res, '输入/输出评审内容')
          },
          onFinished: (res) => {
            console.log(res, '输入/输出评审完成')
            setReviewLoading(false)
            return
            // 开始场景输入
            sceneReviewStartFn()
            // if (current < pageInfo.steps.length - 1) {
            //   setCurrent(current + 1);
            // }
          },
        })
      }
    } catch (error) {
      setReviewLoading(false)
    }
  }
  const sceneReviewStartFn = async () => {
    setSceneReviewStart(true)
    const tokenInfo: any = await getToken()
    console.log(tokenInfo, '获取用户信息token')
    const userInfo: any = await getUserInfo()
    const tenantId: any = cacheGet('tenantId')
    const ctx = { tokenInfo, tenantId, userId: userInfo?.id || 'anonymous' };
    const promiseArray = pageInfo.steps.map((item) => {
      return fetchDataById({
        pageName: pageInfo.pageName,
        pageDesc: pageInfo.pageDesc,
        pageInputDesc: pageInfo.pageInputDesc,
        pageOutputDesc: pageInfo.pageOutputDesc,
        steps: [item]
      },ctx)
    })
    const result = await Promise.all(promiseArray)
    console.log(result,'场景规划结果')
  //   const arr = pageInfo.steps.map((item, index) => {
  //   // pageInfo.steps & result
  //   return {
  //     ...item,
  //     ...result[index],
  //   }
  // }
  // pageInfo.steps = arr
  }
  const fetchDataById = (data: any,ctx:any) => {
    const agentId = reviewAgentId['场景规划师']
    const stepData: any = mentionsRef.current?.getMentionsData?.()
    console.log('获取场景规划数据:', stepData)
    const fileData: any[] = []
    stepData.localFile.forEach((item: any) => {
      fileData.push({
        type: item.fileType || 'document',
        transfer_method: 'local_file',
        upload_file_id: item.id,
      })
    })
    return new Promise((resolve) => {
      sseChat2.start({
      url: '/dify/broker/agent/stream',
      headers: {
        'Content-Type': 'application/json',
        Token: ctx.tokenInfo || '',
      },

      body: {
        insId: '1',
        bizType: 'app:agent',
        bizId: agentId,
        agentId: agentId,
        path: '/chat-messages',
        query: stepData.query || '',
        difyJson: {
          inputs: {
            Token: ctx.tokenInfo || '',
            tenantid: ctx.tenantId || '',
            pageInfo: JSON.stringify(data),
          },
          response_mode: 'streaming',
          user: ctx.userId || 'anonymous',
          conversation_id: '',
          query: stepData.query || '',
          // files: fileData,
          // pageInfo: JSON.stringify(pageInfo),
        },
      },
      // body: {
      //   insId: '1',
      //   bizType: 'app:agent',
      //   bizId: agentId,
      //   agentId: agentId,
      //   path: '/chat-messages',
      //   query: stepData.query || '',
      //   difyJson: {
      //     inputs: {
      //       Token: ctx.tokenInfo || '',
      //       tenantid: ctx.tenantId || '',
      //       pageInfo: JSON.stringify(data),
      //     },
      //     response_mode: 'streaming',
      //     user: ctx?.id || 'anonymous',
      //     conversation_id: '',
      //     query: stepData.query || '',
      //     files: fileData,
      //     // pageInfo: JSON.stringify(pageInfo),
      //   },
      // },
      query: {},
      message: stepData.query || '',
      // onMessage: (res: any) => {
      //   console.log(res, '输入/输出评审内容')
      // },
      onFinished: (res) => {
        console.log(res, '输入/输出评审完成')
        resolve(res)
        // if (current < pageInfo.steps.length - 1) {
        //   setCurrent(current + 1);
        // }
      },
    })
    })
    console.log(data)
    
    
  }
  // 提交操作
  const getSubmitInfo = async (item: any) => {
    const ref = refs.current[current]
    const childData = ref?.current?.getMentionsData?.() // 获取子组件暴露方法
    console.log('提交数据:', childData)
    // if (item?.agentId) {
    //   const tokenInfo = await getToken();
    //   const userInfo = await getUserInfo();
    //   const tenantId = cacheGet("tenantId");

    //   const fileData: any[] = [];
    //   childData?.localFile?.forEach((item: any) => {
    //     fileData.push({
    //       type: item.fileType || "document",
    //       transfer_method: "local_file",
    //       upload_file_id: item.id,
    //     });
    //   });

    //   sseChat.start({
    //     url: "/dify/broker/agent/stream",
    //     headers: {
    //       "Content-Type": "application/json",
    //       Token: tokenInfo || "",
    //     },
    //     body: {
    //       insId: "1",
    //       bizType: "app:agent",
    //       bizId: item.agentId,
    //       agentId: item.agentId,
    //       path: "/chat-messages",
    //       query: childData?.query || "",
    //       difyJson: {
    //         inputs: {
    //           docFiles: fileData,
    //           Token: tokenInfo || "",
    //           tenantid: tenantId || "",
    //           outputTemplate: null, // templateFile 可根据实际情况补上
    //         },
    //         response_mode: "streaming",
    //         user: userInfo?.id || "anonymous",
    //         conversation_id: "",
    //         query: childData?.query || "",
    //       },
    //     },
    //     query: {},
    //     message: childData?.query || "",
    //     onFinished: () => {
    //       if (current < pageInfo.steps.length - 1) {
    //         setCurrent(current + 1);
    //       }
    //     },
    //   });
    // } else {
    // 存到父组件 state
    setStepData2((prev: any) => ({
      ...prev,
      [current]: childData,
    }))
    if (current < pageInfo.steps.length - 1) {
      setCurrent(current + 1)
    }
    // }
  }

  const handleSessionSelect = (sessionId: string | null, sessionData?: any) => {
    if (sessionId) {
      setCurrentSessionId(sessionId)
      console.log('切换到会话:', sessionId, sessionData)
    } else {
      // 创建新会话
      setCurrentSessionId('')
      console.log('创建新会话')
    }
  }

  const handleSidebarToggle = (visible: boolean) => {
    setSidebarVisible(visible)
  }

  const onChange = (value: number) => {
    console.log('onChange:', value)
    setCurrent(value)
  }

  // 监听refs变化，帮助调试
  useEffect(() => {
    console.log('refs变化:', refs.current)
  }, [refs.current])

  // 监听当前步骤变化
  useEffect(() => {
    console.log('当前步骤变化:', current, currentStep)
  }, [current, currentStep])

  // 监听步骤数据变化
  useEffect(() => {
    console.log('步骤数据变化:', stepData)
  }, [stepData])

  // 新增：使用子组件暴露方法的示例函数
  const handleGetStepData = () => {
    const currentRef = refs.current[current]
    if (currentRef?.current) {
      // 获取当前步骤的数据
      const currentStepData = currentRef.current.getCurrentStepData()
      console.log('当前步骤数据:', currentStepData)

      // 获取指定步骤的数据
      const step0Data = currentRef.current.getStepData(0)
      const step1Data = currentRef.current.getStepData(1)
      console.log('步骤0数据:', step0Data)
      console.log('步骤1数据:', step1Data)
    }
  }

  return (
    <Flex vertical className="interview-page" align="center">
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      {/* 左侧边栏 */}
      {/* <LeftSidebarModule 
        sessionId={currentSessionId}
        onSessionSelect={handleSessionSelect}
        onSidebarToggle={handleSidebarToggle}
      /> */}
      <Flex
        className={`main-content ${sidebarVisible ? 'sidebar-open' : ''}`}
        vertical
        justify="center"
      >
        <HeaderCom
          mainTitle={pageInfo.pageName}
          subTitle={pageInfo.pageDesc}
        ></HeaderCom>
        <Flex vertical className="interview-page-con" justify="center">
          {!started && (
            <div style={{ width: '60vw', margin: '0 auto' }}>
              <MentionsComponent
                // agentId="0f31b4cb-5752-409d-b47d-38a9c1e162d6"
                agentId="ea543945-0d56-42a3-98bb-f06a3f4dd2f6"
                ref={mentionsRef}
              />
              <div
                className="start-btn"
                style={{ width: '100%', textAlign: 'center' }}
              >
                <Button
                  type="primary"
                  block
                  onClick={() => {
                    getStartInfo()
                  }}
                  style={{ marginRight: 8 }}
                >
                  开始
                </Button>
              </div>
            </div>
          )}

          {started && (
            <>
              <Steps
                current={current}
                style={{ marginBottom: 24 }}
                className="custome-steps"
                onChange={onChange}
              >
                {pageInfo.steps.map((item) => (
                  <Steps.Step key={item.title} title={item.title} />
                ))}
              </Steps>

              <div className="dynamic-component">
                <Suspense fallback={<div>加载中...</div>}>
                  {DynamicComponent ? (
                    <DynamicComponent
                      setGlobalLoading={setGlobalLoading}
                      globalLoading={globalLoading}
                      leftTabs={currentStep.leftTabs}
                      currentStep={currentStep.title}
                      stepIndex={current}
                      fileData={uploadedFileData} // 传递文件数据
                      startData={stepData.startData} // 传递简历解析数据（查询内容、文件等）
                      questionData={{ queryData: stepData2[0] }} // 传递智能出题数据
                      agentId={currentStep.agentId} // 传递当前步骤的agentId
                    />
                  ) : (
                    <div>组件未找到</div>
                  )}
                </Suspense>
              </div>

              <div style={{ textAlign: 'center' }}>
                {current > 0 && (
                  <Button
                    onClick={() => setCurrent(current - 1)}
                    style={{ marginRight: 8 }}
                  >
                    上一步
                  </Button>
                )}
                {current < pageInfo.steps.length - 1 && (
                  <Button
                    type="primary"
                    onClick={() => getSubmitInfo(currentStep)}
                  >
                    下一步
                  </Button>
                )}
                {/* {currentStep.isExport && (
                <Button type="primary" onClick={() => console.log("导出操作")}>
                  导出
                </Button>
              )} */}
                {current == pageInfo.steps.length - 1 && (
                  <Button onClick={getSubmitInfo} style={{ marginRight: 8 }}>
                    重新开始
                  </Button>
                )}

                {current == pageInfo.steps.length - 1 && (
                  <Button
                    type="primary"
                    onClick={getSubmitInfo}
                    style={{ marginRight: 8 }}
                  >
                    完成面试
                  </Button>
                )}

                {current == pageInfo.steps.length - 1 && (
                  <Button type="primary" onClick={getSubmitInfo}>
                    下载报告
                  </Button>
                )}
              </div>
            </>
          )}
        </Flex>
      </Flex>
    </Flex>
  )
}

export default DraftPage
