.interview-page{
  width: 100%;
  height: 100vh;
  padding-left: 0;
  transition: padding-left 0.3s ease;
  // 主内容区域
  .main-content {
    width: 100%;
    transition: margin-left 0.3s ease;

    &.sidebar-open {
      margin-left: 300px;
    }
  }
  .interview-page-con{
    // margin-top: 20px;
    flex: 1;
    // width: 100vw
    .step-header {
      width: 100%;
      position: relative;
      border-bottom: 1px solid var(--ant-color-border);
      .step-header-left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        position: absolute;
        left: 20px;
        top: 12px;
        .step-left-title {
          font-size: 22x;
          font-weight: 600;
          color: #333;
          margin: 0;
        }
        .step-left-desc {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }
    }
    .custome-steps {
      margin: 24px 20vw 24px;
      width: 60vw;
    }
  
    .dynamic-component {
      min-height: 300px;
      margin-bottom: var(--ant-margin-lg);
      width: 80vw;
      margin: 0 auto;
      height: calc(100vh - 130px);
    }
  }
  
  .start-btn {
    margin-top: var(--ant-margin-xxl);
  }
}