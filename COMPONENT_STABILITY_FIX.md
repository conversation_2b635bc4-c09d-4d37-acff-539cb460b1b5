# 组件稳定性修复方案

## 问题分析

组件被多次初始化的原因：

1. **依赖循环问题**：`loadComponent` 函数依赖 `loadedComponents` 状态，每次状态更新都会重新创建函数
2. **重复渲染**：正常渲染区域和强制渲染区域可能同时渲染同一个组件
3. **组件包装器不稳定**：每次调用 `loadComponent` 都创建新的 ComponentWrapper
4. **状态更新触发重渲染**：`setLoadedComponents` 导致组件重新渲染

## 解决方案

### 1. 使用 useRef 存储组件缓存

**之前的问题**：
```javascript
const [loadedComponents, setLoadedComponents] = useState({});
// 每次状态更新都会触发重新渲染
```

**修复后**：
```javascript
const loadedComponentsRef = useRef({});
// 使用 ref 存储，不会触发重新渲染
```

### 2. 移除依赖循环

**之前的问题**：
```javascript
const loadComponent = useCallback(
  (stepIndex) => {
    // ...
  },
  [pageInfo.steps, loadedComponents] // loadedComponents 导致循环依赖
);
```

**修复后**：
```javascript
const loadComponent = useCallback(
  (stepIndex) => {
    // 直接使用 ref，不依赖状态
    if (loadedComponentsRef.current[stepIndex]) {
      return loadedComponentsRef.current[stepIndex];
    }
    // ...
  },
  [pageInfo.steps] // 移除 loadedComponents 依赖
);
```

### 3. 稳定的组件包装器

**之前的问题**：
```javascript
// 每次调用都创建新的组件
const ComponentWrapper = (props) => (
  <LazyComponent ref={refs.current[stepIndex]} {...props} />
);
```

**修复后**：
```javascript
// 使用 React.memo 创建稳定的组件
const ComponentWrapper = React.memo((props) => (
  <LazyComponent ref={refs.current[stepIndex]} {...props} />
));

// 设置显示名称便于调试
ComponentWrapper.displayName = `${componentName}_${stepIndex}`;

// 缓存到 ref 中
loadedComponentsRef.current[stepIndex] = ComponentWrapper;
```

### 4. 统一渲染逻辑

**之前的问题**：
- 正常渲染区域
- 隐藏渲染区域（用于强制渲染）
- 两个区域可能同时渲染同一个组件

**修复后**：
```javascript
// 统一在一个地方渲染
const shouldRender = isCurrentStep && (shouldShowComponent || forceRenderStep === stepIndex);

return (
  <div
    key={`component-${stepIndex}`}
    style={{
      display: shouldShowComponent ? "block" : "none",
    }}
  >
    <Component key={`inner-${stepIndex}`} {...props} />
  </div>
);
```

### 5. 稳定的 key 值

添加稳定的 key 值确保 React 正确识别组件：
```javascript
<div key={`component-${stepIndex}`}>
  <Component key={`inner-${stepIndex}`} {...props} />
</div>
```

## 工作原理

1. **首次加载**：组件被创建并缓存到 `loadedComponentsRef.current[stepIndex]`
2. **后续调用**：直接从 ref 中返回已缓存的组件，不会重新创建
3. **强制渲染**：通过 `forceRenderStep` 状态控制组件显示，但不重新创建组件
4. **状态隔离**：组件缓存使用 ref，不会触发父组件重新渲染

## 预期效果

1. **组件只初始化一次**：每个步骤的组件只会被创建一次
2. **ref 稳定可用**：组件创建后 ref 保持稳定，可以正常调用方法
3. **性能优化**：避免不必要的组件重新创建和渲染
4. **状态保持**：组件内部状态在切换步骤时得以保持

## 测试验证

现在当您：
1. 点击评审页面的确定按钮
2. `unitContinueScenario` 设置 `inputReview: true`
3. 调用 `getSubmit` 方法

应该看到：
- 组件初始化日志只出现一次
- `ref.current` 不再是 undefined
- 组件方法可以正常调用

## 调试信息

组件包装器现在有显示名称，便于调试：
```javascript
ComponentWrapper.displayName = `${componentName}_${stepIndex}`;
```

在 React DevTools 中可以看到类似 `SplitviewModule_0` 的组件名称。
