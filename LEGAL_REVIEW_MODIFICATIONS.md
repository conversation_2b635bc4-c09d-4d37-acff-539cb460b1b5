# 法审页面修改说明

## 修改目标

修改 `src/pages/legalReview/index.tsx` 页面，使页面在有内容回显时不需要重新加载，直接回显内容。即点击上一步或下一步时，页面展示对应的组件而不重新创建组件。

## 主要修改内容

### 1. 按需加载机制

**原来的实现：**

- 使用 `DynamicComponent` 根据当前步骤动态加载组件
- 每次切换步骤时重新创建组件，导致组件状态丢失

**修改后的实现：**

- 使用 `loadedComponents` 状态缓存已加载的组件
- 只有在输入输出评审完毕且需要显示内容时才加载组件
- 已加载的组件会被缓存，切换步骤时不会重新创建
- 组件状态得以保持，实现内容回显

### 2. 组件渲染逻辑

**原来的实现：**

```jsx
{
  DynamicComponent ? <DynamicComponent {...props} /> : <div>组件未找到</div>;
}
```

**修改后的实现：**

```jsx
{
  pageInfo.steps.map((step, stepIndex) => {
    const isCurrentStep = stepIndex === currentRef.current;
    const shouldShowComponent =
      stepData[stepIndex]?.inputReview &&
      (stepData[stepIndex]?.outputReview || !isOutputReview.current);

    // 只有当前步骤且应该显示组件时才加载组件
    if (!isCurrentStep || !shouldShowComponent) {
      return null;
    }

    // 按需加载组件
    const Component = loadComponent(stepIndex);

    return (
      <div key={stepIndex}>
        <Component {...props} />
      </div>
    );
  });
}
```

### 3. 组件引用管理

- 为每个步骤的组件创建独立的 ref
- 确保组件引用在整个生命周期中保持稳定
- 支持父组件调用子组件方法

### 4. 状态管理优化

- 保持 `stepData` 状态结构不变
- 确保组件间数据传递的一致性
- 维护评审状态的正确性

## 技术实现细节

### 按需加载组件逻辑

```javascript
// 已加载的组件缓存
const [loadedComponents, setLoadedComponents] = useState({});

// 按需加载组件
const loadComponent = useCallback(
  (stepIndex) => {
    if (loadedComponents[stepIndex]) {
      return loadedComponents[stepIndex];
    }

    const step = pageInfo.steps[stepIndex];
    const match = step?.content.match(/<(\w+)\s*\/>/);
    const componentName = match?.[1];

    if (componentName) {
      const modulePath = `/src/businessComponents/${componentName}/index.tsx`;
      const loader = modules[modulePath];

      if (loader) {
        // 创建 ref（如果不存在）
        if (!refs.current[stepIndex]) {
          refs.current[stepIndex] = createRef();
        }

        const LazyComponent = React.lazy(loader);
        const ComponentWrapper = (props) => (
          <LazyComponent ref={refs.current[stepIndex]} {...props} />
        );

        // 缓存已加载的组件
        setLoadedComponents((prev) => ({
          ...prev,
          [stepIndex]: ComponentWrapper,
        }));

        return ComponentWrapper;
      }
    }

    return null;
  },
  [pageInfo.steps, loadedComponents]
);
```

### 显示控制逻辑

- `isCurrentStep`: 判断是否为当前激活步骤
- `shouldShowComponent`: 判断是否应该显示组件内容（输入输出评审完毕）
- 只有满足条件时才加载和渲染组件，否则返回 `null`

## 修改效果

### 用户体验改进

1. **无缝切换**: 点击上一步/下一步时，页面内容立即切换，无加载延迟
2. **状态保持**: 用户在组件中的输入、选择等状态得以保持
3. **快速回显**: 已填写的内容在切换步骤后能够立即显示

### 性能优化

1. **按需加载**: 只有在需要显示内容时才加载组件，避免不必要的初始化
2. **组件缓存**: 已加载的组件会被缓存，切换步骤时不会重新创建
3. **内存优化**: 相比预加载所有组件，按需加载减少了内存占用
4. **响应速度**: 已缓存的组件切换响应更快，用户体验更流畅

## 兼容性说明

- 保持原有的 API 接口不变
- 子组件的 props 传递方式保持一致
- 评审流程逻辑保持不变
- 支持所有现有的业务组件

## 注意事项

1. 组件只有在输入输出评审完毕且需要显示时才会加载，初始加载时间更优化
2. 已加载的组件状态会在整个页面生命周期中保持，需要注意内存使用
3. 如果需要重置某个步骤的状态，需要通过组件的方法来实现，而不是重新创建组件
4. 组件缓存机制确保了相同步骤的组件不会重复加载
