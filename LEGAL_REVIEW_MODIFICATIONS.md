# 法审页面修改说明

## 修改目标
修改 `src/pages/legalReview/index.tsx` 页面，使页面在有内容回显时不需要重新加载，直接回显内容。即点击上一步或下一步时，页面展示对应的组件而不重新创建组件。

## 主要修改内容

### 1. 组件预加载机制
**原来的实现：**
- 使用 `DynamicComponent` 根据当前步骤动态加载组件
- 每次切换步骤时重新创建组件，导致组件状态丢失

**修改后的实现：**
- 使用 `AllDynamicComponents` 预加载所有步骤的组件
- 所有组件保持挂载状态，通过显示/隐藏控制可见性
- 组件状态得以保持，实现内容回显

### 2. 组件渲染逻辑
**原来的实现：**
```jsx
{DynamicComponent ? (
  <DynamicComponent {...props} />
) : (
  <div>组件未找到</div>
)}
```

**修改后的实现：**
```jsx
{pageInfo.steps.map((step, stepIndex) => {
  const Component = AllDynamicComponents[stepIndex];
  const isCurrentStep = stepIndex === currentRef.current;
  const shouldShowComponent = 
    stepData[stepIndex]?.inputReview &&
    (stepData[stepIndex]?.outputReview || !isOutputReview.current);

  return (
    <div
      key={stepIndex}
      style={{
        display: isCurrentStep && shouldShowComponent ? "block" : "none",
      }}
    >
      <Component {...props} />
    </div>
  );
})}
```

### 3. 组件引用管理
- 为每个步骤的组件创建独立的 ref
- 确保组件引用在整个生命周期中保持稳定
- 支持父组件调用子组件方法

### 4. 状态管理优化
- 保持 `stepData` 状态结构不变
- 确保组件间数据传递的一致性
- 维护评审状态的正确性

## 技术实现细节

### 预加载组件逻辑
```javascript
const AllDynamicComponents = useMemo(() => {
  const components = {};
  
  pageInfo.steps.forEach((step, index) => {
    const match = step.content.match(/<(\w+)\s*\/>/);
    const componentName = match?.[1];

    if (componentName) {
      const modulePath = `/src/businessComponents/${componentName}/index.tsx`;
      const loader = modules[modulePath];

      if (loader) {
        if (!refs.current[index]) {
          refs.current[index] = createRef();
        }

        const LazyComponent = React.lazy(loader);
        components[index] = (props) => (
          <LazyComponent ref={refs.current[index]} {...props} />
        );
      }
    }
  });

  return components;
}, [pageInfo.steps]);
```

### 显示控制逻辑
- `isCurrentStep`: 判断是否为当前激活步骤
- `shouldShowComponent`: 判断是否应该显示组件内容
- 通过 CSS `display` 属性控制组件可见性

## 修改效果

### 用户体验改进
1. **无缝切换**: 点击上一步/下一步时，页面内容立即切换，无加载延迟
2. **状态保持**: 用户在组件中的输入、选择等状态得以保持
3. **快速回显**: 已填写的内容在切换步骤后能够立即显示

### 性能优化
1. **减少重渲染**: 组件不再重新创建，减少不必要的渲染
2. **内存优化**: 虽然所有组件都保持挂载，但通过显示/隐藏控制，避免了重复的组件初始化开销
3. **响应速度**: 步骤切换响应更快，用户体验更流畅

## 兼容性说明
- 保持原有的 API 接口不变
- 子组件的 props 传递方式保持一致
- 评审流程逻辑保持不变
- 支持所有现有的业务组件

## 注意事项
1. 所有步骤的组件会在页面加载时预加载，可能会增加初始加载时间
2. 组件状态会在整个页面生命周期中保持，需要注意内存使用
3. 如果需要重置某个步骤的状态，需要通过组件的方法来实现，而不是重新创建组件
