import type { ConfigEnv, UserConfig } from "vite";
import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import checker from "vite-plugin-checker";
import viteCompression from "vite-plugin-compression";
import svgr from "vite-plugin-svgr";
import path from "path";

export default defineConfig(({ mode, command }: ConfigEnv): UserConfig => {
  // 加载对应环境的 .env 文件内容
  const env = loadEnv(mode, process.cwd());

  return {
    base: "/",

    publicDir: "public",

    resolve: {
      alias: [
        { find: "@", replacement: path.resolve(process.cwd(), "src") },
        {
          find: "@projects",
          replacement: path.resolve(process.cwd(), "projects"),
        },
      ],
    },

    optimizeDeps: {
      include: ["dayjs/locale/zh-cn"],
    },

    build: {
      outDir: "out/copilot",
      assetsDir: "assets",
      copyPublicDir: true,
      rollupOptions: {
        output: {
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
        },
      },
    },

    plugins: [
      tailwindcss(),
      react(),
      checker({ typescript: true }),
      viteCompression(),
      svgr({
        svgrOptions: { icon: true, exportType: "named" },
        include: "**/assets/icons/*.svg",
      }),
    ],

    define: {
      // 注入自定义环境变量，用于前端代码中识别当前构建环境
      __APP_ENV__: JSON.stringify(mode),
    },

    server: {
      host: "0.0.0.0",
      port: 5173,
      proxy: {
        "/dify": {
          target: env.VITE_BASE_AI_API,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dify/, ""),
        },
        "/langwell-api": {
          target: env.VITE_BASE_API,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/langwell-api/, "/langwell-api"),
        },
        "/lamp-api": {
          target: env.VITE_BASE_API,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/lamp-api/, "/lamp-api"),
        },
        "/common-split-api": {
          target: "http://************:18080",
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path: string) =>
            path.replace(new RegExp(`^/common-split-api`), ""),
        },
      },
    },
  };
});
